// pages/game2048/game2048.js
const audioManager = require('../../utils/audioManager');
const serverConfig = require('../../config/server-config');
const userManager = require('../../utils/userManager');

Page({
    data: {
        // 游戏状态
        board: [],
        score: 0,
        bestScore: 0,
        moves: 0,
        gameOver: false,
        isWin: false,
        isNewRecord: false,
        
        // 触摸相关
        touchStartX: 0,
        touchStartY: 0,
        
        // 排行榜相关
        showRankingModal: false,
        loadingRanking: false,
        rankingList: [],
        
        // 提交分数相关
        submitting: false,

        // 防作弊相关
        gameStartTime: 0,
        totalMoveTime: 0,
        lastMoveTime: 0,

        // 音效相关
        soundEnabled: audioManager.soundEnabled,

        // 排名相关
        currentRank: 0,
        loadingRank: false,

        // 点击音效控制
        lastClickTime: 0,
        
        // 使用微信云开发，无需配置API地址
    },

    onLoad() {
        // 初始化用户管理器
        userManager.init();
        // 初始化游戏
        this.initGame();
        // 加载最高分
        this.loadBestScore();
        // 用户昵称通过微信登录获取，不再需要本地加载
        // 禁用页面滚动
        this.disablePageScroll();
        // 同步音效设置
        this.setData({
            soundEnabled: audioManager.soundEnabled
        });
    },

    onUnload() {
        // 恢复页面滚动
        this.enablePageScroll();
        // 清理音频资源
        try {
            audioManager.destroy();
        } catch (e) {
            console.log('清理音频资源失败:', e);
        }
    },

    onShow() {
        // 页面显示时重新初始化音频管理器
        try {
            audioManager.reinitialize();
            // 同步音效设置
            this.setData({
                soundEnabled: audioManager.soundEnabled
            });
        } catch (e) {
            console.log('页面显示时初始化音频管理器失败:', e);
        }
    },

    onHide() {
        // 页面隐藏时不销毁音频管理器，只是暂停播放
        console.log('页面隐藏，音频管理器保持活跃状态');
    },

    // 禁用页面滚动
    disablePageScroll() {
        wx.pageScrollTo({
            scrollTop: 0,
            duration: 0
        });
    },

    // 恢复页面滚动
    enablePageScroll() {
        // 页面卸载时恢复滚动（如果需要）
    },

    // 播放音效
    playSound(type) {
        try {
            // 确保音频管理器可用
            if (audioManager.isDestroyed) {
                console.log('音频管理器已销毁，重新初始化');
                audioManager.reinitialize();
            }

            switch(type) {
                case 'move':
                    audioManager.playMoveSound();
                    break;
                case 'newGame':
                    audioManager.playNewGameSound();
                    break;
                case 'win':
                    audioManager.playWinSound();
                    break;
                case 'gameOver':
                    audioManager.playGameOverSound();
                    break;
                case 'click':
                    audioManager.playClickSound();
                    break;
                case 'ranking':
                    audioManager.playRankingSound();
                    break;
                default:
                    console.log('未知音效类型:', type);
            }
        } catch (e) {
            console.log('播放音效失败:', type, e);
        }
    },

    // 切换音效
    toggleSound() {
        const newSoundEnabled = audioManager.toggleSound();
        this.setData({
            soundEnabled: newSoundEnabled
        });

        // 播放测试音效
        if (newSoundEnabled) {
            this.playSound('move');
        }

        wx.showToast({
            title: newSoundEnabled ? '🔊 ikun音效已开启（请在页面找出一些ikun声音）' : '🔇 ikun音效已关闭',
            icon: 'none',
            duration: 2000
        });
    },

    // 测试点击音效（调试用）
    testClickSound() {
        audioManager.testClickSound();
    },

    // 初始化游戏
    initGame() {
        const board = this.createEmptyBoard();
        this.addRandomTile(board);
        this.addRandomTile(board);
        
        this.setData({
            board: board,
            score: 0,
            moves: 0,
            gameOver: false,
            isWin: false,
            isNewRecord: false,
            gameStartTime: Date.now(),
            totalMoveTime: 0,
            lastMoveTime: 0
        });
    },

    // 创建空棋盘
    createEmptyBoard() {
        const board = [];
        for (let i = 0; i < 4; i++) {
            board[i] = [];
            for (let j = 0; j < 4; j++) {
                board[i][j] = 0;
            }
        }
        return board;
    },

    // 添加随机数字块
    addRandomTile(board) {
        const emptyCells = [];
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                if (board[i][j] === 0) {
                    emptyCells.push({row: i, col: j});
                }
            }
        }
        
        if (emptyCells.length > 0) {
            const randomCell = emptyCells[Math.floor(Math.random() * emptyCells.length)];
            board[randomCell.row][randomCell.col] = Math.random() < 0.9 ? 2 : 4;
        }
    },

    // 触摸开始
    onTouchStart(e) {
        // 阻止默认滑动行为
        e.preventDefault && e.preventDefault();

        this.setData({
            touchStartX: e.touches[0].clientX,
            touchStartY: e.touches[0].clientY
        });
    },

    // 触摸结束
    onTouchEnd(e) {
        // 阻止默认滑动行为
        e.preventDefault && e.preventDefault();

        if (this.data.gameOver) return;

        const deltaX = e.changedTouches[0].clientX - this.data.touchStartX;
        const deltaY = e.changedTouches[0].clientY - this.data.touchStartY;
        const minDistance = 50; // 最小滑动距离

        if (Math.abs(deltaX) < minDistance && Math.abs(deltaY) < minDistance) {
            return; // 滑动距离太小，忽略
        }

        let direction = '';
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            // 水平滑动
            direction = deltaX > 0 ? 'right' : 'left';
        } else {
            // 垂直滑动
            direction = deltaY > 0 ? 'down' : 'up';
        }

        this.move(direction);
    },

    // 触摸移动（阻止页面滚动）
    onTouchMove(e) {
        // 阻止页面滚动
        e.preventDefault && e.preventDefault();
        return false;
    },

    // 全局点击事件
    onGlobalClick() {
        try {
            // 限制点击音效频率，避免过于频繁
            const currentTime = Date.now();
            if (currentTime - this.data.lastClickTime > 100) { // 100ms内只播放一次
                this.setData({
                    lastClickTime: currentTime
                });
                this.playSound('click');
            }
        } catch (e) {
            console.log('全局点击事件处理失败:', e);
        }
    },



    // 移动逻辑
    move(direction) {
        const newBoard = JSON.parse(JSON.stringify(this.data.board));
        let moved = false;
        let scoreIncrease = 0;
        
        if (direction === 'left') {
            for (let i = 0; i < 4; i++) {
                const result = this.moveRowLeft(newBoard[i]);
                newBoard[i] = result.row;
                moved = moved || result.moved;
                scoreIncrease += result.score;
            }
        } else if (direction === 'right') {
            for (let i = 0; i < 4; i++) {
                const result = this.moveRowRight(newBoard[i]);
                newBoard[i] = result.row;
                moved = moved || result.moved;
                scoreIncrease += result.score;
            }
        } else if (direction === 'up') {
            for (let j = 0; j < 4; j++) {
                const column = [newBoard[0][j], newBoard[1][j], newBoard[2][j], newBoard[3][j]];
                const result = this.moveRowLeft(column);
                for (let i = 0; i < 4; i++) {
                    newBoard[i][j] = result.row[i];
                }
                moved = moved || result.moved;
                scoreIncrease += result.score;
            }
        } else if (direction === 'down') {
            for (let j = 0; j < 4; j++) {
                const column = [newBoard[0][j], newBoard[1][j], newBoard[2][j], newBoard[3][j]];
                const result = this.moveRowRight(column);
                for (let i = 0; i < 4; i++) {
                    newBoard[i][j] = result.row[i];
                }
                moved = moved || result.moved;
                scoreIncrease += result.score;
            }
        }
        
        if (moved) {
            this.addRandomTile(newBoard);
            const newScore = this.data.score + scoreIncrease;
            const newMoves = this.data.moves + 1;
            const currentTime = Date.now();
            const moveTime = currentTime - (this.data.lastMoveTime || this.data.gameStartTime);

            // 播放移动音效
            this.playSound('move');

            this.setData({
                board: newBoard,
                score: newScore,
                moves: newMoves,
                totalMoveTime: this.data.totalMoveTime + moveTime,
                lastMoveTime: currentTime
            });

            // 检查游戏状态
            this.checkGameStatus(newBoard, newScore);
        }
    },

    // 向左移动一行
    moveRowLeft(row) {
        const newRow = row.filter(cell => cell !== 0);
        let moved = newRow.length !== row.length || !this.arraysEqual(newRow, row.filter(cell => cell !== 0));
        let score = 0;
        
        // 合并相同数字
        for (let i = 0; i < newRow.length - 1; i++) {
            if (newRow[i] === newRow[i + 1]) {
                newRow[i] *= 2;
                score += newRow[i];
                newRow.splice(i + 1, 1);
                moved = true;
            }
        }
        
        // 补齐到4位
        while (newRow.length < 4) {
            newRow.push(0);
        }
        
        return { row: newRow, moved, score };
    },

    // 向右移动一行
    moveRowRight(row) {
        const reversed = row.slice().reverse();
        const result = this.moveRowLeft(reversed);
        return {
            row: result.row.reverse(),
            moved: result.moved,
            score: result.score
        };
    },

    // 数组比较
    arraysEqual(a, b) {
        return a.length === b.length && a.every((val, i) => val === b[i]);
    },

    // 检查游戏状态
    checkGameStatus(board, score) {
        // 检查是否获胜（达到2048）
        let hasWon = false;
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                if (board[i][j] >= 2048) {
                    hasWon = true;
                    break;
                }
            }
        }
        
        // 检查是否还能移动
        const canMove = this.canMove(board);
        
        if (hasWon || !canMove) {
            const isNewRecord = score > this.data.bestScore;
            if (isNewRecord) {
                this.saveBestScore(score);
            }

            // 播放游戏结束音效
            if (hasWon) {
                this.playSound('win');
            } else {
                this.playSound('gameOver');
            }

            this.setData({
                gameOver: true,
                isWin: hasWon,
                isNewRecord: isNewRecord,
                bestScore: Math.max(score, this.data.bestScore),
                loadingRank: true,
                currentRank: 0
            });

            // 获取当前分数在排行榜中的位次
            this.getCurrentRank(score);

            // 显示游戏结束提示
            this.showGameOverToast(hasWon, score, isNewRecord);
        }
    },

    // 获取当前分数的排名
    getCurrentRank(score) {
        // 模拟获取排名（实际应该调用API）
        // 这里先用本地计算一个大概的排名
        const estimatedRank = this.estimateRank(score);

        this.setData({
            currentRank: estimatedRank,
            loadingRank: false
        });

        // 如果有API，可以调用真实的排名接口
        /*
        wx.request({
            url: 'https://your-api-domain.com/api/game2048/get-rank',
            method: 'POST',
            data: { score: score },
            success: (res) => {
                if (res.data && res.data.success) {
                    this.setData({
                        currentRank: res.data.rank || estimatedRank,
                        loadingRank: false
                    });
                }
            },
            fail: () => {
                this.setData({
                    currentRank: estimatedRank,
                    loadingRank: false
                });
            }
        });
        */
    },

    // 估算排名（基于分数）
    estimateRank(score) {
        if (score >= 50000) return Math.floor(Math.random() * 5) + 1;       // 前5名（超级高手）
        if (score >= 30000) return Math.floor(Math.random() * 15) + 6;      // 6-20名（顶级高手）
        if (score >= 20000) return Math.floor(Math.random() * 30) + 21;     // 21-50名（高手）
        if (score >= 15000) return Math.floor(Math.random() * 50) + 51;     // 51-100名（熟练+）
        if (score >= 8000) return Math.floor(Math.random() * 100) + 101;    // 101-200名（熟练）
        if (score >= 5000) return Math.floor(Math.random() * 150) + 201;    // 201-350名（新手+）
        if (score >= 2048) return Math.floor(Math.random() * 200) + 351;    // 351-550名（新手）
        return Math.floor(Math.random() * 300) + 551;                       // 551名以后
    },

    // 显示游戏结束提示
    showGameOverToast(hasWon, score, isNewRecord) {
        setTimeout(() => {
            const rank = this.data.currentRank;
            let title = hasWon ? '🎉 恭喜获胜！' : '😢 游戏结束';
            let content = `本次分数：${score}分\n`;

            if (isNewRecord) {
                content += '🏆 新的个人最高分！\n';
            }

            if (rank > 0) {
                if (rank <= 10) {
                    content += `🥇 华航排行榜第${rank}名！太棒了！`;
                } else if (rank <= 50) {
                    content += `🏅 华航${rank}名！很不错！`;
                } else if (rank <= 100) {
                    content += `📈 华航排行榜第${rank}名！继续加油！`;
                } else {
                    content += `📊 华航排行榜第${rank}名`;
                }
            } else {
                content += '快去提交分数到华航排行榜吧！';
            }

            wx.showModal({
                title: title,
                content: content,
                showCancel: false,
                confirmText: '知道了',
                confirmColor: hasWon ? '#27ae60' : '#e74c3c'
            });
        }, 500); // 延迟500ms显示，让用户看到最终状态
    },

    // 检查是否还能移动
    canMove(board) {
        // 检查是否有空格
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                if (board[i][j] === 0) {
                    return true;
                }
            }
        }
        
        // 检查是否有相邻相同数字
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                const current = board[i][j];
                if ((i < 3 && board[i + 1][j] === current) ||
                    (j < 3 && board[i][j + 1] === current)) {
                    return true;
                }
            }
        }
        
        return false;
    },

    // 新游戏（添加确认提示）
    newGame() {
        // 如果当前有分数（无论游戏是否结束），都显示确认对话框
        if (this.data.score > 0) {
            const gameStatus = this.data.gameOver ?
                (this.data.isWin ? '（已胜利）' : '（已结束）') :
                '（进行中）';

            wx.showModal({
                title: '开始新游戏',
                content: `当前分数：${this.data.score}${gameStatus}\n确定要开始新游戏吗？当前进度将丢失。`,
                confirmText: '确定',
                cancelText: '取消',
                confirmColor: '#e17055',
                success: (res) => {
                    if (res.confirm) {
                        this.startNewGame();
                    }
                }
            });
        } else {
            this.startNewGame();
        }
    },

    // 开始新游戏
    startNewGame() {
        this.playSound('newGame');
        this.initGame();
        wx.showToast({
            title: '新游戏开始！',
            icon: 'none',
            duration: 1000
        });
    },

    // 加载最高分
    loadBestScore() {
        const bestScore = wx.getStorageSync('game2048_best_score') || 0;
        this.setData({ bestScore });
    },

    // 保存最高分
    saveBestScore(score) {
        wx.setStorageSync('game2048_best_score', score);
    },

    // 加载用户昵称功能已移除，使用微信登录信息

    // 昵称输入功能已移除，使用微信登录信息

    // 提交分数到真实排行榜
    async submitScore() {
        const { score, moves } = this.data;

        try {
            // 检查用户是否已登录
            if (!userManager.checkLoginStatus()) {
                // 用户未登录，要求登录
                await userManager.requireLogin();
            }

            // 获取用户信息
            const userInfo = userManager.getUserInfo();
            if (!userInfo || !userInfo.nickName) {
                wx.showToast({
                    title: '获取用户信息失败，请重新登录',
                    icon: 'none'
                });
                return;
            }

            this.setData({ submitting: true });

            // 使用“游戏昵称”（优先用户自定义，其次微信昵称）提交分数
            const displayName = userManager.getDisplayNickname();
            this.submitScoreToServer(displayName, score, moves, userInfo);

        } catch (error) {
            console.error('提交分数失败:', error);
            this.setData({ submitting: false });

            if (error.message === '用户取消登录') {
                wx.showToast({
                    title: '需要登录才能提交分数',
                    icon: 'none'
                });
            } else {
                wx.showToast({
                    title: '提交失败，请重试',
                    icon: 'none'
                });
            }
        }
    },

    // 提交分数到服务器
    submitScoreToServer(nickname, score, moves, userInfo) {
        const gameTime = Date.now() - this.data.gameStartTime;
        const avgMoveTime = this.data.totalMoveTime / moves;

        // 基本的防作弊检查
        if (gameTime < 3000) { // 游戏时间少于30秒
            wx.showToast({
                title: '游戏时间过短，请正常游戏',
                icon: 'none'
            });
            this.setData({ submitting: false });
            return;
        }

        if (avgMoveTime < 200) { // 平均每步少于200毫秒
            wx.showToast({
                title: '操作过快，请正常游戏',
                icon: 'none'
            });
            this.setData({ submitting: false });
            return;
        }

        const scoreData = {
            nickname: nickname,
            score: score,
            moves: moves,
            gameTime: gameTime || null,
            avgMoveTime: avgMoveTime || null,
            timestamp: new Date().toISOString(),
            deviceInfo: this.getDeviceInfo(),
            // 添加微信用户信息
            wxUserInfo: {
                nickName: userInfo.nickName,
                avatarUrl: userInfo.avatarUrl,
                code: userInfo.code // 微信登录code，服务器可用来获取openid
            }
        };

        // 提交到阿里云服务器
        this.submitToCloudFunction(scoreData);
    },

    // 获取设备信息（用于防刷分）
    getDeviceInfo() {
        try {
            const systemInfo = wx.getSystemInfoSync();
            return {
                platform: systemInfo.platform,
                version: systemInfo.version,
                model: systemInfo.model
            };
        } catch (e) {
            return {};
        }
    },

    // 提交到阿里云服务器
    submitToCloudFunction(scoreData) {
        console.log('使用阿里云服务器提交分数...');

        // 直接使用阿里云服务器API
        this.submitToHttpAPI(scoreData);
    },

    // 提交到HTTP API（备用方案）
    submitToHttpAPI(scoreData) {
        wx.request({
            url: serverConfig.getApiUrl('submitScore'), // 使用配置文件中的地址
            method: 'POST',
            header: {
                'Content-Type': 'application/json'
            },
            data: scoreData,
            success: (res) => {
                this.setData({ submitting: false });
                if (res.statusCode === 200 && res.data.success) {
                    wx.showToast({
                        title: '分数提交成功！',
                        icon: 'success'
                    });
                    // 刷新排行榜
                    this.loadRankingData();
                } else {
                    wx.showToast({
                        title: res.data.message || '提交失败',
                        icon: 'none'
                    });
                }
            },
            fail: (err) => {
                console.error('HTTP提交失败:', err);
                // 网络失败时保存到本地缓存
                this.saveScoreToLocalCache(scoreData);
            }
        });
    },

    // 保存分数到本地缓存（网络失败时的备用方案）
    saveScoreToLocalCache(scoreData) {
        try {
            // 获取现有的本地分数记录
            const localScores = wx.getStorageSync('game2048_local_scores') || [];

            // 添加新分数记录
            const newScore = {
                ...scoreData,
                id: Date.now(),
                timestamp: new Date().toISOString(),
                synced: false // 标记为未同步到服务器
            };

            localScores.unshift(newScore);

            // 只保留最近的50条记录
            const limitedScores = localScores.slice(0, 50);

            // 保存到本地存储
            wx.setStorageSync('game2048_local_scores', limitedScores);

            // 更新本地排行榜缓存
            this.updateLocalRankingCache(limitedScores);

            this.setData({ submitting: false });

            wx.showToast({
                title: '网络异常，分数已保存到本地',
                icon: 'none',
                duration: 2500
            });

            console.log('分数已保存到本地缓存:', newScore);

        } catch (error) {
            console.error('保存分数到本地失败:', error);
            this.setData({ submitting: false });
            wx.showToast({
                title: '保存失败，请重试',
                icon: 'none'
            });
        }
    },

    // 更新本地排行榜缓存
    updateLocalRankingCache(localScores) {
        // 按分数排序，生成排行榜
        const sortedScores = localScores
            .sort((a, b) => {
                if (b.score !== a.score) {
                    return b.score - a.score; // 分数降序
                }
                return a.moves - b.moves; // 步数升序
            })
            .slice(0, 20) // 只取前20名
            .map((score, index) => ({
                id: score.id,
                nickname: score.nickname,
                score: score.score,
                moves: score.moves,
                rank: index + 1,
                timeAgo: this.formatTimeAgo(score.timestamp),
                isLocal: true // 标记为本地数据
            }));

        // 更新排行榜缓存
        wx.setStorageSync('game2048_ranking_cache', sortedScores);

        console.log('本地排行榜已更新，共', sortedScores.length, '条记录');
    },

    // 显示排行榜
    showRanking() {
        // 播放华航排行榜特殊音效
        this.playSound('ranking');

        this.setData({
            showRankingModal: true,
            loadingRanking: true,
            rankingList: []
        });

        // 加载排行榜数据
        this.loadRankingData();
    },

    // 测试API连接（调试用）
    testAPIConnection() {
        console.log('=== 测试API连接 ===');
        console.log('提交分数API:', serverConfig.getApiUrl('submitScore'));
        console.log('获取排行榜API:', serverConfig.getApiUrl('getRanking'));

        wx.showActionSheet({
            itemList: ['测试排行榜API', '测试提交分数API', '检查数据库连接', '查看API详情'],
            success: (res) => {
                switch (res.tapIndex) {
                    case 0:
                        this.testRankingAPI();
                        break;
                    case 1:
                        this.testSubmitAPI();
                        break;
                    case 2:
                        this.testDatabaseConnection();
                        break;
                    case 3:
                        this.showAPIDetails();
                        break;
                }
            }
        });
    },

    // 测试排行榜API
    testRankingAPI() {
        wx.showActionSheet({
            itemList: ['测试当前API', '测试不同参数', '检查数据库连接', '强制刷新缓存'],
            success: (res) => {
                switch (res.tapIndex) {
                    case 0:
                        this.doRankingAPITest();
                        break;
                    case 1:
                        this.testDifferentParams();
                        break;
                    case 2:
                        this.checkDatabaseConnection();
                        break;
                    case 3:
                        this.forceRefreshCache();
                        break;
                }
            }
        });
    },

    // 执行排行榜API测试
    doRankingAPITest() {
        wx.showLoading({ title: '测试中...' });

        const testUrl = serverConfig.getApiUrl('getRanking') + '?limit=50&_t=' + Date.now();
        console.log('测试API地址:', testUrl);

        wx.request({
            url: testUrl,
            method: 'GET',
            success: (res) => {
                wx.hideLoading();
                console.log('排行榜API测试成功:', res);

                let content = `🔍 深度API诊断\n\n`;
                content += `📡 完整URL: ${testUrl}\n`;
                content += `📊 HTTP状态码: ${res.statusCode}\n`;
                content += `🕐 响应时间: ${new Date().toLocaleTimeString()}\n`;
                content += `📋 数据类型: ${Array.isArray(res.data) ? '数组' : typeof res.data}\n`;

                if (Array.isArray(res.data)) {
                    content += `📈 记录数量: ${res.data.length}\n\n`;

                    // 详细分析数据特征
                    if (res.data.length === 2) {
                        const firstRecord = res.data[0];
                        const secondRecord = res.data[1];

                        if (firstRecord.nickname === '你好' && firstRecord.score === 6200 &&
                            secondRecord.nickname === '测试用户' && secondRecord.score === 2048) {
                            content += `🚨 确认：这是硬编码的测试数据！\n\n`;
                            content += `📋 问题分析：\n`;
                            content += `• 数据完全固定，不会变化\n`;
                            content += `• 时间戳也是固定的\n`;
                            content += `• 说明API没有查询真实数据库\n\n`;
                            content += `🔧 可能的原因：\n`;
                            content += `1. API代码中硬编码了测试数据\n`;
                            content += `2. 连接了错误的数据库表\n`;
                            content += `3. 数据库查询失败，返回默认数据\n`;
                            content += `4. 使用了缓存的测试数据\n`;
                            content += `5. API路由指向了错误的处理函数\n\n`;
                        }
                    }

                    content += `📝 详细数据:\n`;
                    res.data.forEach((item, index) => {
                        content += `${index + 1}. 昵称: ${item.nickname}\n`;
                        content += `   分数: ${item.score}\n`;
                        content += `   步数: ${item.moves}\n`;
                        content += `   时间: ${item.timestamp}\n`;
                        content += `   ID: ${item.id}\n\n`;
                    });
                } else {
                    content += `📄 响应数据: ${JSON.stringify(res.data, null, 2)}`;
                }

                wx.showModal({
                    title: '🔍 深度API诊断结果',
                    content: content,
                    showCancel: true,
                    cancelText: '关闭',
                    confirmText: '生成报告',
                    success: (modalRes) => {
                        if (modalRes.confirm) {
                            const report = `华航小游戏API诊断报告
生成时间: ${new Date().toLocaleString()}

API地址: ${testUrl}
状态码: ${res.statusCode}
数据条数: ${Array.isArray(res.data) ? res.data.length : 0}

问题确认: 返回固定测试数据
- 昵称"你好"，分数6200
- 昵称"测试用户"，分数2048

建议检查:
1. 服务器端API代码是否硬编码
2. 数据库连接是否正确
3. 查询语句是否有问题
4. 是否有缓存机制

完整响应数据:
${JSON.stringify(res.data, null, 2)}`;

                            wx.setClipboardData({
                                data: report,
                                success: () => {
                                    wx.showToast({
                                        title: '诊断报告已复制',
                                        icon: 'success'
                                    });
                                }
                            });
                        }
                    }
                });
            },
            fail: (err) => {
                wx.hideLoading();
                console.error('排行榜API测试失败:', err);
                wx.showModal({
                    title: '❌ API连接失败',
                    content: `错误信息: ${err.errMsg}\n\nAPI地址: ${testUrl}\n\n可能的原因:\n1. 服务器未启动\n2. 网络连接问题\n3. API地址错误\n4. 防火墙阻止`,
                    showCancel: false
                });
            }
        });
    },

    // 测试不同参数
    testDifferentParams() {
        wx.showActionSheet({
            itemList: ['limit=100', 'limit=1', '无参数', '添加时间戳'],
            success: (res) => {
                let params = '';
                switch (res.tapIndex) {
                    case 0: params = '?limit=100'; break;
                    case 1: params = '?limit=1'; break;
                    case 2: params = ''; break;
                    case 3: params = '?_t=' + Date.now(); break;
                }

                const testUrl = serverConfig.getApiUrl('getRanking') + params;
                wx.showLoading({ title: '测试参数...' });

                wx.request({
                    url: testUrl,
                    method: 'GET',
                    success: (testRes) => {
                        wx.hideLoading();
                        wx.showModal({
                            title: '参数测试结果',
                            content: `参数: ${params}\n记录数: ${Array.isArray(testRes.data) ? testRes.data.length : '非数组'}\n状态码: ${testRes.statusCode}`,
                            showCancel: false
                        });
                    },
                    fail: (err) => {
                        wx.hideLoading();
                        wx.showToast({ title: '测试失败', icon: 'none' });
                    }
                });
            }
        });
    },

    // 检查阿里云数据库连接
    checkDatabaseConnection() {
        wx.showModal({
            title: '🗄️ 数据库查询问题诊断',
            content: `根据您的发现，数据库中有更多数据，但API只返回了id=4和id=5的记录。\n\n🔍 问题分析：\nAPI查询逻辑有问题，可能有不必要的WHERE条件限制。\n\n� 需要检查的SQL查询：\n1. 是否有 WHERE id IN (4,5)\n2. 是否有其他限制条件\n3. 是否连接了错误的表\n\n� 修复步骤：\n1. 找到API查询代码\n2. 删除限制条件\n3. 使用正确的ORDER BY\n4. 重启服务测试`,
            showCancel: true,
            cancelText: '关闭',
            confirmText: '查看修复指南',
            success: (res) => {
                if (res.confirm) {
                    wx.showModal({
                        title: '� API修复指南',
                        content: `正确的查询应该是：\n\nSELECT id, nickname, score, moves, created_at as timestamp FROM scores ORDER BY score DESC LIMIT 50;\n\n❌ 删除这些限制：\n• WHERE id IN (4,5)\n• WHERE status = 'active'\n• WHERE created_at > '某个日期'\n\n🔍 搜索命令：\ngrep -r "WHERE.*id" /www/wwwroot/\ngrep -r "IN.*(" /www/wwwroot/`,
                        showCancel: false,
                        confirmText: '复制搜索命令',
                        success: (modalRes) => {
                            if (modalRes.confirm) {
                                wx.setClipboardData({
                                    data: `grep -r "WHERE.*id" /www/wwwroot/game2048-api/ /www/wwwroot/api.huahang.me/
grep -r "IN.*(" /www/wwwroot/game2048-api/ /www/wwwroot/api.huahang.me/
grep -r "SELECT.*FROM.*scores" /www/wwwroot/game2048-api/ /www/wwwroot/api.huahang.me/`
                                });
                            }
                        }
                    });
                }
            }
        });
    },

    // 强制刷新缓存
    forceRefreshCache() {
        wx.showLoading({ title: '刷新缓存...' });

        // 清除本地缓存
        try {
            wx.removeStorageSync('game2048_ranking_cache');
        } catch (e) {}

        // 使用时间戳强制刷新
        const refreshUrl = serverConfig.getApiUrl('getRanking') + '?limit=50&refresh=true&_t=' + Date.now();

        wx.request({
            url: refreshUrl,
            method: 'GET',
            header: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            },
            success: (res) => {
                wx.hideLoading();
                wx.showModal({
                    title: '🔄 缓存刷新结果',
                    content: `刷新完成！\n\n记录数量: ${Array.isArray(res.data) ? res.data.length : '非数组'}\n\n${Array.isArray(res.data) && res.data.length === 2 && res.data[0].nickname === '你好' ? '⚠️ 仍然是测试数据，问题在服务器端' : '✅ 数据已更新'}`,
                    showCancel: false
                });
            },
            fail: (err) => {
                wx.hideLoading();
                wx.showToast({ title: '刷新失败', icon: 'none' });
            }
        });
    },

    // 测试提交分数API
    testSubmitAPI() {
        wx.showModal({
            title: '测试提交API',
            content: '这将提交一条测试数据到服务器，确定继续吗？',
            success: (res) => {
                if (res.confirm) {
                    const testData = {
                        nickname: '测试用户_' + Date.now(),
                        score: 1024,
                        moves: 50,
                        gameTime: 60000,
                        timestamp: new Date().toISOString(),
                        wxUserInfo: {
                            nickName: '测试用户',
                            avatarUrl: '',
                            code: 'test_code'
                        }
                    };

                    wx.showLoading({ title: '提交测试数据...' });

                    wx.request({
                        url: serverConfig.getApiUrl('submitScore'),
                        method: 'POST',
                        data: testData,
                        success: (submitRes) => {
                            wx.hideLoading();
                            wx.showModal({
                                title: '提交测试结果',
                                content: `状态码: ${submitRes.statusCode}\n响应: ${JSON.stringify(submitRes.data)}`,
                                showCancel: false
                            });
                        },
                        fail: (err) => {
                            wx.hideLoading();
                            wx.showModal({
                                title: '提交测试失败',
                                content: `错误: ${err.errMsg}`,
                                showCancel: false
                            });
                        }
                    });
                }
            }
        });
    },

    // 测试数据库连接
    testDatabaseConnection() {
        wx.showModal({
            title: '数据库连接测试',
            content: '请在服务器端检查以下内容：\n\n1. 数据库连接是否正常\n2. game2048.scores表是否存在\n3. 表中是否有真实数据\n4. API查询语句是否正确\n\n建议直接在数据库中执行：\nSELECT * FROM game2048.scores ORDER BY score DESC LIMIT 10;',
            showCancel: false,
            confirmText: '知道了'
        });
    },

    // 显示API详情
    showAPIDetails() {
        const details = `API配置详情：

服务器域名: ${serverConfig.current.domain}
服务器IP: ${serverConfig.current.ip}
端口: ${serverConfig.current.port}

排行榜API: ${serverConfig.getApiUrl('getRanking')}
提交分数API: ${serverConfig.getApiUrl('submitScore')}

当前返回的数据：
- 第1条：昵称"你好"，分数6200
- 第2条：昵称"测试用户"，分数2048

如果这不是您期望的数据库内容，请检查：
1. 服务器端API是否连接了正确的数据库
2. 数据库中是否有其他数据
3. API查询条件是否正确`;

        wx.showModal({
            title: 'API配置详情',
            content: details,
            showCancel: false,
            confirmText: '复制配置',
            success: (res) => {
                if (res.confirm) {
                    wx.setClipboardData({
                        data: details
                    });
                }
            }
        });
    },

    // 加载排行榜数据（直接从阿里云服务器）
    async loadRankingData() {
        try {
            console.log('从阿里云服务器加载排行榜数据...');
            console.log('使用API地址:', serverConfig.getApiUrl('getRanking'));

            // 直接使用阿里云服务器API
            const apiData = await loadRankingFromAPIWithRetry();
            console.log('从阿里云服务器加载排行榜数据成功，共', apiData ? apiData.length : 0, '条记录');
            console.log('API返回的原始数据:', apiData);

            if (!apiData || !Array.isArray(apiData)) {
                console.error('API返回的数据格式不正确:', apiData);
                throw new Error('API返回数据格式错误');
            }

            // 处理数据并更新界面
            const rankingList = this.processRankingData(apiData);
            console.log('处理后的排行榜数据:', rankingList);

            // 计算“我的排名”
            const meName = userManager.getDisplayNickname();
            let myRank = 0;
            let myScore = 0;
            rankingList.forEach((item, idx) => {
                if (!myRank && (item.nickname === meName)) {
                    myRank = idx + 1;
                    myScore = item.score;
                }
            });

            this.setData({
                loadingRanking: false,
                rankingList: rankingList,
                currentUserRank: myRank,
                currentUserScore: myScore
            });

            return apiData;

        } catch (error) {
            console.error('排行榜加载错误:', error.message);

            // 如果失败，显示空列表
            this.setData({
                loadingRanking: false,
                rankingList: []
            });

            this.handleRankingError('网络连接失败，请检查网络后重试');
            return [];
        }
    },



    // 从HTTP API加载排行榜
    loadRankingFromAPI() {
        return new Promise((resolve, reject) => {
            console.log('开始请求排行榜API...');

            wx.request({
                url: serverConfig.getApiUrl('getRanking') + '?limit=50',
                method: 'GET',
                timeout: 30000, // 增加到30秒
                header: {
                    'content-type': 'application/json',
                    'User-Agent': 'WeChat-MiniProgram'
                },
                enableHttp2: false, // 禁用HTTP2，使用HTTP1.1
                enableQuic: false,  // 禁用QUIC
                success: function(res) {
                    console.log('API请求成功:', res);
                    console.log('API响应数据:', res.data);
                    if (res.statusCode === 200 && res.data) {
                        // 处理不同的响应格式
                        if (Array.isArray(res.data)) {
                            // 直接返回数组格式
                            resolve(res.data);
                        } else if (res.data.success && res.data.data) {
                            // 标准API响应格式
                            resolve(res.data.data);
                        } else if (res.data.data) {
                            // 只有data字段
                            resolve(res.data.data);
                        } else {
                            console.error('API返回格式不正确:', res.data);
                            reject(new Error('API返回格式错误'));
                        }
                    } else {
                        reject(new Error(`HTTP状态码: ${res.statusCode}`));
                    }
                },
                fail: function(err) {
                    console.error('API请求失败:', err);
                    reject(new Error(`请求失败: ${err.errMsg}`));
                }
            });
        });
    },



    // 处理排行榜数据
    processRankingData(rawData) {
        console.log('处理排行榜原始数据:', rawData);

        if (!Array.isArray(rawData)) {
            console.error('排行榜数据不是数组格式:', rawData);
            return [];
        }

        return rawData.map((item, index) => {
            console.log(`处理第${index + 1}条数据:`, item);

            return {
                id: item.id || item._id || index,
                nickname: item.nickname || '匿名用户',
                score: item.score || 0,
                moves: item.moves || 0,
                avatarUrl: item.avatarUrl || this.getDefaultAvatar(item.nickname),
                timeAgo: this.formatTimeAgo(item.created_at || item.timestamp || item.createdAt || new Date()),
                rank: index + 1
            };
        });
    },

    // 获取默认头像
    getDefaultAvatar(nickname) {
        // 使用在线头像服务生成头像
        const colors = ['667eea', '764ba2', 'ff6b6b', '4ecdc4', 'ffa726'];

        if (!nickname) {
            return 'https://ui-avatars.com/api/?name=User&background=667eea&color=fff&size=80&rounded=true';
        }

        // 根据昵称的第一个字符选择颜色
        const charCode = nickname.charCodeAt(0);
        const colorIndex = charCode % colors.length;
        const backgroundColor = colors[colorIndex];

        // 生成头像URL
        const name = encodeURIComponent(nickname.substring(0, 2)); // 取前两个字符
        return `https://ui-avatars.com/api/?name=${name}&background=${backgroundColor}&color=fff&size=80&rounded=true&font-size=0.6`;
    },

    // 格式化时间
    formatTimeAgo(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diff = now - time;

        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));

        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 30) return `${days}天前`;
        return time.toLocaleDateString();
    },

    // 处理排行榜加载错误
    handleRankingError(message) {
        console.log('排行榜加载错误:', message);

        // 显示错误提示
        wx.showToast({
            title: '网络错误，请重试',
            icon: 'none',
            duration: 2000
        });
    },

    // 关闭排行榜
    closeRanking() {
        this.setData({
            showRankingModal: false
        });
    },

    // 分享功能
    onShareAppMessage() {
        return {
            title: `我在华航2048小游戏中得了${this.data.score}分！马上华航第一！`,
            path: '/pages/game2048/game2048',
            imageUrl: '/images/share-2048.png'
        };
    },

    // 分享到朋友圈
    onShareTimeline() {
        return {
            title: `华航2048小游戏 - 我得了${this.data.score}分！马上华航第一`,
            query: '',
            imageUrl: '/images/share-2048.png'
        };
    }
});

// 添加带重试的请求函数
async function loadRankingFromAPIWithRetry() {
    const maxRetries = 2; // 减少重试次数
    let lastError;
    
    for (let i = 0; i < maxRetries; i++) {
        try {
            console.log(`尝试请求排行榜 (${i + 1}/${maxRetries})`);
            
            const result = await new Promise((resolve, reject) => {
                wx.request({
                    url: serverConfig.getApiUrl('getRanking') + '?limit=50',
                    method: 'GET',
                    timeout: 15000, // 15秒超时
                    dataType: 'json',
                    success: function(res) {
                        console.log('API响应:', res);
                        console.log('API响应数据详情:', JSON.stringify(res.data, null, 2));
                        if (res.statusCode === 200) {
                            // 处理不同的响应格式
                            if (Array.isArray(res.data)) {
                                // 直接返回数组格式
                                console.log('检测到数组格式响应，数据条数:', res.data.length);
                                resolve(res.data);
                            } else if (res.data && res.data.success && res.data.data) {
                                // 标准API响应格式 {success: true, data: [...]}
                                console.log('检测到标准API格式响应，数据条数:', res.data.data.length);
                                resolve(res.data.data);
                            } else if (res.data && res.data.data) {
                                // 只有data字段 {data: [...]}
                                console.log('检测到data字段格式响应，数据条数:', res.data.data.length);
                                resolve(res.data.data);
                            } else {
                                console.error('API返回格式不正确:', res.data);
                                reject(new Error(`API返回格式错误: ${JSON.stringify(res.data)}`));
                            }
                        } else {
                            reject(new Error(`HTTP状态码: ${res.statusCode}`));
                        }
                    },
                    fail: function(err) {
                        console.error('请求失败详情:', err);
                        console.error('错误类型:', typeof err.errMsg);
                        console.error('完整错误对象:', JSON.stringify(err));

                        let errorMsg = '网络连接失败';
                        if (err.errMsg) {
                            if (err.errMsg.includes('CONNECTION_CLOSED')) {
                                errorMsg = 'SSL证书验证失败，请联系管理员';
                            } else if (err.errMsg.includes('timeout')) {
                                errorMsg = '请求超时，请重试';
                            } else {
                                errorMsg = `网络错误: ${err.errMsg}`;
                            }
                        }

                        reject(new Error(errorMsg));
                    }
                });
            });
            
            console.log('排行榜请求成功');
            return result;
            
        } catch (error) {
            lastError = error;
            console.log(`第${i + 1}次请求失败:`, error.message);
            
            if (i < maxRetries - 1) {
                // 等待后重试，增加等待时间
                const waitTime = (i + 1) * 5000; // 5秒, 10秒
                console.log(`等待${waitTime}ms后重试...`);
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
        }
    }

    throw lastError;
}
