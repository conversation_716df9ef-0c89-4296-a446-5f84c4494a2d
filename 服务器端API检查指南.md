# 🔍 服务器端API检查指南

## 🚨 问题描述

排行榜API返回的始终是这两条测试数据：
- 第1条：昵称"你好"，分数6200，步数438
- 第2条：昵称"测试用户"，分数2048，步数150

## 🔧 服务器端检查清单

### 1. 检查数据库连接

**在服务器上执行以下SQL查询：**
```sql
-- 检查表是否存在
SHOW TABLES LIKE 'scores';

-- 查看表结构
DESCRIBE game2048.scores;

-- 查看所有数据
SELECT * FROM game2048.scores ORDER BY score DESC;

-- 统计总记录数
SELECT COUNT(*) as total_count FROM game2048.scores;
```

### 2. 检查API实现

**检查您的排行榜API代码（通常在服务器端）：**

```javascript
// 示例：正确的API实现
app.get('/api/game2048/ranking', async (req, res) => {
    try {
        const limit = req.query.limit || 50;
        
        // 🔴 检查这里是否硬编码了测试数据
        const query = `
            SELECT id, nickname, score, moves, created_at as timestamp 
            FROM game2048.scores 
            ORDER BY score DESC 
            LIMIT ?
        `;
        
        const results = await db.query(query, [parseInt(limit)]);
        
        console.log('数据库查询结果:', results); // 添加日志
        
        res.json(results);
    } catch (error) {
        console.error('排行榜API错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});
```

### 3. 常见问题排查

#### 问题1：硬编码测试数据
**检查是否有类似代码：**
```javascript
// ❌ 错误：硬编码测试数据
const testData = [
    { id: 1, nickname: "你好", score: 6200, moves: 438 },
    { id: 2, nickname: "测试用户", score: 2048, moves: 150 }
];
res.json(testData);
```

#### 问题2：数据库连接错误
**检查数据库配置：**
```javascript
// 确认数据库连接配置
const dbConfig = {
    host: 'your-database-host',
    user: 'your-username', 
    password: 'your-password',
    database: 'game2048'  // 🔴 确认数据库名称
};
```

#### 问题3：表名或字段名错误
**检查SQL查询：**
```sql
-- 确认表名和字段名
SELECT 
    id,
    nickname,  -- 🔴 确认字段名
    score,
    moves,
    created_at
FROM game2048.scores  -- 🔴 确认表名
ORDER BY score DESC;
```

#### 问题4：缓存问题
**检查是否有缓存机制：**
```javascript
// 如果使用了缓存，清除缓存
cache.clear('ranking_data');

// 或者禁用缓存进行测试
app.get('/api/game2048/ranking', (req, res) => {
    res.set('Cache-Control', 'no-cache');
    // ... API逻辑
});
```

## 🛠️ 调试步骤

### 第一步：直接查询数据库
```bash
# 登录到您的阿里云数据库
mysql -h your-host -u your-username -p

# 切换到数据库
USE game2048;

# 查看所有数据
SELECT * FROM scores ORDER BY score DESC LIMIT 10;
```

### 第二步：检查API日志
在服务器端API中添加详细日志：
```javascript
app.get('/api/game2048/ranking', async (req, res) => {
    console.log('=== 排行榜API调用 ===');
    console.log('请求参数:', req.query);
    
    try {
        const results = await db.query(query, [limit]);
        console.log('数据库查询结果数量:', results.length);
        console.log('前3条数据:', results.slice(0, 3));
        
        res.json(results);
    } catch (error) {
        console.error('数据库查询错误:', error);
        res.status(500).json({ error: error.message });
    }
});
```

### 第三步：测试API端点
```bash
# 直接测试API
curl "https://api.huahang.me/api/game2048/ranking?limit=10"

# 或使用浏览器访问
https://api.huahang.me/api/game2048/ranking?limit=10
```

## 🔍 可能的解决方案

### 方案1：如果是硬编码问题
```javascript
// 移除硬编码，使用真实数据库查询
app.get('/api/game2048/ranking', async (req, res) => {
    const limit = req.query.limit || 50;
    const results = await db.query(
        'SELECT * FROM game2048.scores ORDER BY score DESC LIMIT ?',
        [limit]
    );
    res.json(results);
});
```

### 方案2：如果是数据库连接问题
```javascript
// 测试数据库连接
app.get('/api/test-db', async (req, res) => {
    try {
        const result = await db.query('SELECT COUNT(*) as count FROM game2048.scores');
        res.json({ 
            success: true, 
            totalRecords: result[0].count,
            message: '数据库连接正常'
        });
    } catch (error) {
        res.json({ 
            success: false, 
            error: error.message 
        });
    }
});
```

### 方案3：如果是表结构问题
```sql
-- 检查并修复表结构
ALTER TABLE game2048.scores 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- 确保有索引
CREATE INDEX idx_score ON game2048.scores(score DESC);
```

## 📋 检查清单

请逐一确认：

- [ ] 数据库中确实有多条真实数据（不只是那两条测试数据）
- [ ] API代码没有硬编码测试数据
- [ ] 数据库连接配置正确
- [ ] SQL查询语句正确
- [ ] 表名和字段名匹配
- [ ] 没有缓存旧数据
- [ ] 服务器日志显示正确的查询结果

## 🚀 立即行动

1. **直接查询数据库**确认数据内容
2. **检查服务器端API代码**查找硬编码
3. **添加调试日志**查看实际查询结果
4. **测试API端点**确认返回数据

---

**🎯 目标：确保API返回数据库中的所有真实数据，而不是固定的测试数据！**
