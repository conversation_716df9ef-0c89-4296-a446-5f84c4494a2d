// pages/index/index.js
Page({
  data: {
    // 2048游戏统计数据
    game2048Stats: {
      bestScore: 0,
      playCount: 0
    }
  },

  onLoad() {
    console.log('华航小游戏首页加载');
    this.loadGameStats();
  },

  onShow() {
    // 每次显示页面时刷新统计数据
    this.loadGameStats();
  },

  // 加载游戏统计数据
  loadGameStats() {
    try {
      // 加载2048游戏统计
      const bestScore = wx.getStorageSync('game2048_best_score') || 0;
      const playCount = wx.getStorageSync('game2048_play_count') || 0;
      
      this.setData({
        'game2048Stats.bestScore': bestScore,
        'game2048Stats.playCount': playCount
      });
    } catch (e) {
      console.error('加载游戏统计失败:', e);
    }
  },

  // 导航到游戏页面
  navigateToGame(e) {
    const gameType = e.currentTarget.dataset.game;
    
    switch (gameType) {
      case '2048':
        // 增加游戏次数统计
        this.incrementPlayCount();
        
        wx.navigateTo({
          url: '/pages/game2048/game2048',
          success: () => {
            console.log('导航到2048游戏成功');
          },
          fail: (err) => {
            console.error('导航到2048游戏失败:', err);
            wx.showToast({
              title: '游戏加载失败',
              icon: 'none'
            });
          }
        });
        break;
      
      default:
        wx.showToast({
          title: '游戏即将推出，敬请期待！',
          icon: 'none',
          duration: 2000
        });
        break;
    }
  },

  // 增加游戏次数统计
  incrementPlayCount() {
    try {
      const currentCount = wx.getStorageSync('game2048_play_count') || 0;
      const newCount = currentCount + 1;
      wx.setStorageSync('game2048_play_count', newCount);
      
      this.setData({
        'game2048Stats.playCount': newCount
      });
    } catch (e) {
      console.error('更新游戏次数失败:', e);
    }
  },

  // goToProfile函数已移除，现在使用底部导航栏


  // 分享功能
  onShareAppMessage() {
    return {
      title: '华航小游戏 - 精品休闲游戏合集',
      path: '/pages/index/index',
      imageUrl: '/images/share-homepage.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '华航小游戏 - 让学习之余更有趣！',
      query: '',
      imageUrl: '/images/share-homepage.png'
    };
  }
});
