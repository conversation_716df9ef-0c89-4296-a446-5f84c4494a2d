# 🔍 阿里云数据库排行榜问题诊断指南

## 🚨 问题确认

**当前状态：** 排行榜API始终返回这两条固定的测试数据：
- 昵称"你好"，分数6200，步数438，时间"2025-07-28T16:09:00.000Z"
- 昵称"测试用户"，分数2048，步数150，时间"2025-07-31T02:23:46.000Z"

**问题分析：** 这明显不是阿里云数据库中的真实数据，而是硬编码的测试数据。

## 🔧 阿里云服务器端检查步骤

### 第一步：登录阿里云服务器

```bash
# SSH登录到您的阿里云ECS服务器
ssh root@your-server-ip

# 或者使用阿里云控制台的远程连接功能
```

### 第二步：检查数据库连接

```bash
# 连接到阿里云RDS数据库
mysql -h your-rds-endpoint.mysql.rds.aliyuncs.com -u your-username -p

# 输入数据库密码后进入MySQL
```

### 第三步：检查数据库和表

```sql
-- 查看所有数据库
SHOW DATABASES;

-- 切换到game2048数据库
USE game2048;

-- 查看所有表
SHOW TABLES;

-- 查看scores表结构
DESCRIBE scores;

-- 查看表中所有数据
SELECT * FROM scores ORDER BY score DESC;

-- 统计总记录数
SELECT COUNT(*) as total_records FROM scores;

-- 查看最近的记录
SELECT * FROM scores ORDER BY created_at DESC LIMIT 10;
```

### 第四步：检查API服务器代码

在您的阿里云服务器上找到处理排行榜API的代码文件：

```bash
# 查找包含ranking的文件
find /path/to/your/project -name "*.js" -exec grep -l "ranking" {} \;

# 或者搜索包含"你好"的文件（测试数据）
find /path/to/your/project -name "*.js" -exec grep -l "你好" {} \;

# 查看API代码
cat /path/to/your/api/file.js
```

## 🔍 常见问题排查

### 问题1：API代码硬编码测试数据

**检查是否有类似代码：**
```javascript
// ❌ 错误：硬编码测试数据
app.get('/api/game2048/ranking', (req, res) => {
    const testData = [
        { id: 1, nickname: "你好", score: 6200, moves: 438, timestamp: "2025-07-28T16:09:00.000Z" },
        { id: 2, nickname: "测试用户", score: 2048, moves: 150, timestamp: "2025-07-31T02:23:46.000Z" }
    ];
    res.json(testData); // 直接返回测试数据
});
```

**正确的实现应该是：**
```javascript
// ✅ 正确：查询阿里云数据库
app.get('/api/game2048/ranking', async (req, res) => {
    try {
        const limit = req.query.limit || 50;
        
        const query = `
            SELECT id, nickname, score, moves, created_at as timestamp 
            FROM scores 
            ORDER BY score DESC 
            LIMIT ?
        `;
        
        const results = await db.query(query, [limit]);
        console.log('从阿里云数据库查询到', results.length, '条记录');
        
        res.json(results);
    } catch (error) {
        console.error('数据库查询错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});
```

### 问题2：数据库连接配置错误

**检查数据库连接配置：**
```javascript
// 确认连接的是正确的阿里云RDS数据库
const dbConfig = {
    host: 'your-rds-endpoint.mysql.rds.aliyuncs.com', // 阿里云RDS地址
    port: 3306,
    user: 'your-username',
    password: 'your-password',
    database: 'game2048',
    charset: 'utf8mb4'
};
```

### 问题3：表名或字段名错误

**确认表结构：**
```sql
-- 检查表名是否正确
SHOW TABLES LIKE '%score%';

-- 检查字段名
DESCRIBE scores;

-- 确认字段名匹配
SELECT 
    id,
    nickname,  -- 确认字段名
    score,
    moves,
    created_at -- 或者是 timestamp 字段
FROM scores 
ORDER BY score DESC 
LIMIT 5;
```

## 🛠️ 修复步骤

### 步骤1：备份当前代码
```bash
# 备份当前API代码
cp /path/to/your/api/file.js /path/to/your/api/file.js.backup
```

### 步骤2：修改API代码
将硬编码的测试数据替换为真实的数据库查询

### 步骤3：添加调试日志
```javascript
app.get('/api/game2048/ranking', async (req, res) => {
    console.log('=== 排行榜API调用 ===');
    console.log('请求时间:', new Date().toISOString());
    console.log('请求参数:', req.query);
    
    try {
        const results = await db.query(query, params);
        console.log('数据库查询成功，记录数:', results.length);
        console.log('前3条数据:', JSON.stringify(results.slice(0, 3), null, 2));
        
        res.json(results);
    } catch (error) {
        console.error('数据库查询失败:', error);
        res.status(500).json({ error: error.message });
    }
});
```

### 步骤4：重启服务
```bash
# 重启Node.js应用（根据您的部署方式）
pm2 restart your-app-name

# 或者
systemctl restart your-service-name

# 或者直接重启
node your-app.js
```

### 步骤5：测试验证
```bash
# 直接测试API
curl "https://api.huahang.me/api/game2048/ranking?limit=10"

# 查看服务器日志
tail -f /path/to/your/logs/app.log
```

## 📋 检查清单

请逐一确认：

- [ ] 已登录阿里云服务器
- [ ] 已连接到阿里云RDS数据库
- [ ] 确认game2048数据库存在
- [ ] 确认scores表存在且有数据
- [ ] 找到了API处理文件
- [ ] 确认API代码没有硬编码测试数据
- [ ] 数据库连接配置指向阿里云RDS
- [ ] 添加了调试日志
- [ ] 重启了服务
- [ ] 测试API返回真实数据

## 🚀 立即行动

1. **登录阿里云控制台**
2. **连接到您的ECS服务器**
3. **连接到RDS数据库，执行上述SQL查询**
4. **找到并检查API代码**
5. **修复硬编码问题**
6. **重启服务并测试**

## 📞 需要提供的信息

如果问题仍然存在，请提供：

1. **数据库查询结果**：
   ```sql
   SELECT * FROM game2048.scores ORDER BY score DESC LIMIT 10;
   SELECT COUNT(*) FROM game2048.scores;
   ```

2. **API代码片段**：处理 `/api/game2048/ranking` 的代码

3. **服务器日志**：API调用时的日志输出

4. **直接API测试结果**：
   ```bash
   curl "https://api.huahang.me/api/game2048/ranking?limit=10"
   ```

---

**🎯 目标：让排行榜API返回阿里云数据库game2048.scores表中的真实数据！**
