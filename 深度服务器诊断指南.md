# 🔍 深度服务器诊断指南

## 🚨 问题确认

API确实返回了固定的测试数据，但在服务器文件中没有找到硬编码。这说明问题可能在以下几个方面：

## 🔧 深度检查步骤

### 第一步：检查所有可能的文件位置

在宝塔终端中执行更全面的搜索：

```bash
# 搜索整个服务器的所有文件
find /www -name "*.js" -exec grep -l "你好" {} \; 2>/dev/null
find /www -name "*.php" -exec grep -l "你好" {} \; 2>/dev/null
find /www -name "*.py" -exec grep -l "你好" {} \; 2>/dev/null

# 搜索配置文件
find /www -name "*.json" -exec grep -l "6200" {} \; 2>/dev/null
find /www -name "*.conf" -exec grep -l "ranking" {} \; 2>/dev/null

# 搜索数据库相关文件
find /www -name "*.sql" -exec grep -l "你好" {} \; 2>/dev/null

# 搜索可能的备份文件
find /www -name "*.bak" -exec grep -l "你好" {} \; 2>/dev/null
find /www -name "*backup*" -exec grep -l "你好" {} \; 2>/dev/null
```

### 第二步：检查数据库本身

可能问题在数据库中就只有这两条测试数据：

```bash
# 连接数据库
mysql -u root -p

# 检查数据库
USE game2048;
SELECT * FROM scores;
SELECT COUNT(*) FROM scores;
DESCRIBE scores;
SHOW CREATE TABLE scores;
```

### 第三步：检查API路由配置

可能API路由指向了错误的处理函数：

```bash
# 搜索路由配置
find /www -name "*.js" -exec grep -l "api/game2048/ranking" {} \; 2>/dev/null
find /www -name "*.js" -exec grep -l "/ranking" {} \; 2>/dev/null

# 查看路由文件内容
grep -r "ranking" /www/wwwroot/game2048-api/ 2>/dev/null
grep -r "ranking" /www/wwwroot/api.huahang.me/ 2>/dev/null
```

### 第四步：检查是否有代理或负载均衡

```bash
# 检查Nginx配置
cat /www/server/nginx/conf/nginx.conf | grep -A 10 -B 10 "api.huahang.me"

# 检查网站配置
find /www/server/panel/vhost/nginx/ -name "*api.huahang.me*" -exec cat {} \;

# 检查是否有upstream配置
grep -r "upstream" /www/server/nginx/conf/
```

### 第五步：检查进程和端口

```bash
# 查看所有Node.js进程
ps aux | grep node
ps aux | grep pm2

# 查看端口占用
netstat -tlnp | grep :3000
netstat -tlnp | grep :4000
netstat -tlnp | grep :8080

# 查看PM2详细信息
pm2 list
pm2 show all
pm2 logs --lines 50
```

## 🔍 可能的问题原因

### 原因1：数据库中确实只有测试数据

**检查方法：**
```sql
-- 查看数据库中的所有数据
SELECT * FROM game2048.scores ORDER BY created_at DESC;

-- 如果确实只有两条测试数据，删除它们
DELETE FROM game2048.scores WHERE nickname IN ('你好', '测试用户');
```

### 原因2：API使用了缓存

**检查方法：**
```bash
# 搜索缓存相关代码
grep -r "cache" /www/wwwroot/game2048-api/ 2>/dev/null
grep -r "redis" /www/wwwroot/game2048-api/ 2>/dev/null
grep -r "memcache" /www/wwwroot/game2048-api/ 2>/dev/null

# 如果使用了Redis，清除缓存
redis-cli
FLUSHALL
```

### 原因3：API连接了错误的数据库

**检查数据库连接配置：**
```bash
# 搜索数据库配置
grep -r "mysql" /www/wwwroot/game2048-api/ 2>/dev/null
grep -r "database" /www/wwwroot/game2048-api/ 2>/dev/null
grep -r "host.*localhost" /www/wwwroot/game2048-api/ 2>/dev/null
```

### 原因4：使用了Mock数据或测试环境

**检查环境配置：**
```bash
# 搜索环境变量
grep -r "NODE_ENV" /www/wwwroot/game2048-api/ 2>/dev/null
grep -r "test" /www/wwwroot/game2048-api/ 2>/dev/null
grep -r "mock" /www/wwwroot/game2048-api/ 2>/dev/null
```

### 原因5：API被代理到了其他服务

**检查代理配置：**
```bash
# 检查Nginx代理配置
grep -r "proxy_pass" /www/server/nginx/conf/
grep -r "proxy_pass" /www/server/panel/vhost/nginx/
```

## 🛠️ 解决方案

### 方案1：如果数据库中确实只有测试数据

```sql
-- 删除测试数据
DELETE FROM game2048.scores WHERE nickname IN ('你好', '测试用户');

-- 插入一些真实数据进行测试
INSERT INTO game2048.scores (nickname, score, moves, created_at) VALUES
('真实用户1', 4096, 200, NOW()),
('真实用户2', 8192, 300, NOW()),
('真实用户3', 2048, 150, NOW());
```

### 方案2：如果有缓存问题

```bash
# 重启所有相关服务
pm2 restart all
systemctl restart nginx
systemctl restart mysql

# 如果有Redis
systemctl restart redis
```

### 方案3：创建一个测试API端点

在API代码中添加一个测试端点：

```javascript
// 添加测试端点
app.get('/api/test/database', async (req, res) => {
    try {
        const query = 'SELECT COUNT(*) as count FROM scores';
        const result = await db.query(query);
        
        const query2 = 'SELECT * FROM scores ORDER BY score DESC LIMIT 5';
        const scores = await db.query(query2);
        
        res.json({
            success: true,
            totalRecords: result[0].count,
            sampleData: scores,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});
```

## 📋 立即执行的检查命令

请在宝塔终端中执行以下命令，并提供结果：

```bash
# 1. 检查数据库实际内容
mysql -u root -p -e "USE game2048; SELECT * FROM scores; SELECT COUNT(*) as total FROM scores;"

# 2. 搜索所有可能包含硬编码的文件
find /www -name "*.js" -o -name "*.php" -o -name "*.py" | xargs grep -l "你好\|6200\|测试用户" 2>/dev/null

# 3. 检查PM2进程
pm2 list
pm2 logs --lines 20

# 4. 检查API是否真的在运行
curl -v "https://api.huahang.me/api/game2048/ranking?limit=3"

# 5. 检查Nginx配置
grep -A 5 -B 5 "api.huahang.me" /www/server/panel/vhost/nginx/*.conf
```

## 🎯 下一步行动

1. **执行上述检查命令**
2. **提供命令执行结果**
3. **根据结果确定具体问题**
4. **实施针对性的解决方案**

---

**关键是要找到数据的真正来源，可能不是硬编码，而是数据库、缓存或代理的问题！**
