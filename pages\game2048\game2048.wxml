<view class="container" bindtap="onGlobalClick">
    <!-- 游戏头部 -->
    <view class="game-header">
        <view class="game-title">🎮 2048小游戏</view>
        <view class="game-subtitle">滑动合并数字，挑战2048！（域名通过后会上线华航排行榜功能，如有想法和建议可到首页加入建议群）</view>
    </view>

    <!-- 游戏信息 -->
    <view class="game-info">
        <view class="info-item">
            <text class="info-label">分数</text>
            <text class="info-value">{{score}}</text>
        </view>
        <view class="info-item">
            <text class="info-label">我的最高分</text>
            <text class="info-value">{{bestScore}}</text>
        </view>
        <view class="info-item">
            <text class="info-label">步数</text>
            <text class="info-value">{{moves}}</text>
        </view>
    </view>

    <!-- 游戏控制 -->
    <view class="game-controls">
        <button class="control-btn" bindtap="newGame">🔄 新游戏</button>
        <button class="control-btn" bindtap="showRanking">🏆 华航榜</button>
        <button class="control-btn sound-btn" bindtap="toggleSound">
            {{soundEnabled ? '🔊' : '🔇'}} {{soundEnabled ? 'ikun' : 'kun'}}
        </button>
    </view>

    <!-- 游戏棋盘 -->
    <view class="game-board"
          bindtouchstart="onTouchStart"
          bindtouchend="onTouchEnd"
          catchtouchmove="onTouchMove">
        <view wx:for="{{board}}" 
              wx:for-index="row" 
              wx:for-item="rowData" 
              wx:key="row" 
              class="board-row">
            <view wx:for="{{rowData}}" 
                  wx:for-index="col" 
                  wx:for-item="cell" 
                  wx:key="col" 
                  class="board-cell cell-{{cell || 'empty'}}">
                <text wx:if="{{cell}}" class="cell-text">{{cell}}</text>
            </view>
        </view>
    </view>

    <!-- 游戏说明 -->
    <view class="game-instructions">
        <view class="instruction-title">🎯 游戏规则</view>
        <view class="instruction-list">
            <text class="instruction-item">• 滑动屏幕移动数字方块</text>
            <text class="instruction-item">• 相同数字碰撞时会合并</text>
            <text class="instruction-item">• 目标是创造出2048方块</text>
            <text class="instruction-item">• 无法移动时游戏结束</text>
            <text class="instruction-item">• 🔊 支持ikun音效反馈</text>
            <text class="instruction-item">• ⚠️ 新游戏有确认提示防误触</text>
        </view>

        <view class="instruction-title">📊 分数机制</view>
        <view class="instruction-list">
            <text class="instruction-item">• 只有合并方块才能获得分数</text>
            <text class="instruction-item">• 合并分数 = 合并后的数字大小</text>
            <text class="instruction-item">• 达成2048通常需要3,000-8,000分</text>
        </view>

        <!-- 分数示例 -->
        <view class="score-examples">
            <view class="example-row">
                <view class="example-merge">2+2=4</view>
                <view class="example-score">+4分</view>
            </view>
            <view class="example-row">
                <view class="example-merge">64+64=128</view>
                <view class="example-score">+128分</view>
            </view>
            <view class="example-row">
                <view class="example-merge">1024+1024=2048</view>
                <view class="example-score">+2048分</view>
            </view>
        </view>

        <view class="instruction-title">🏆 分数等级</view>
        <view class="level-grid">
            <view class="level-card bronze">
                <view class="level-icon">🥉</view>
                <view class="level-name">新手</view>
                <view class="level-score">2048-8000分</view>
            </view>
            <view class="level-card silver">
                <view class="level-icon">🥈</view>
                <view class="level-name">熟练</view>
                <view class="level-score">8000-20000分</view>
            </view>
            <view class="level-card gold">
                <view class="level-icon">🥇</view>
                <view class="level-name">高手</view>
                <view class="level-score">20000分以上</view>
            </view>
        </view>
        <view class="instruction-list">
            <text class="instruction-item">• 🚀 继续挑战4096、8192获得更高分！</text>
        </view>

        <view class="instruction-title">💡 高分技巧</view>
        <view class="instruction-list">
            <text class="instruction-item">• 🏠 角落策略：将最大数字保持在角落</text>
            <text class="instruction-item">• ➡️ 单向移动：避免向最大数字相反方向移动</text>
            <text class="instruction-item">• 📏 保持秩序：让数字按大小顺序排列</text>
            <text class="instruction-item">• ⏰ 耐心规划：提前思考合并路径</text>
        </view>
    </view>

    <!-- 游戏结束弹窗 -->
    <view wx:if="{{gameOver}}" class="game-over-modal">
        <view class="modal-content">
            <view class="modal-title">{{isWin ? '🎉 恭喜获胜！' : '😢 游戏结束'}}</view>
            <view class="modal-score">
                <text class="score-label">本次分数：</text>
                <text class="score-value">{{score}}</text>
            </view>
            <view wx:if="{{isNewRecord}}" class="new-record">🏆 新纪录！</view>

            <!-- 排名信息 -->
            <view class="rank-info">
                <view wx:if="{{loadingRank}}" class="loading-rank">
                    <text class="loading-text">正在计算排名...</text>
                </view>
                <view wx:elif="{{currentRank > 0}}" class="current-rank">
                    <text class="rank-label">华航排行榜位次：</text>
                    <text class="rank-value {{currentRank <= 10 ? 'top-rank' : currentRank <= 50 ? 'good-rank' : 'normal-rank'}}">
                        第{{currentRank}}名
                    </text>
                    <text wx:if="{{currentRank <= 10}}" class="rank-badge">🥇</text>
                    <text wx:elif="{{currentRank <= 50}}" class="rank-badge">🏅</text>
                    <text wx:elif="{{currentRank <= 100}}" class="rank-badge">📈</text>
                </view>
            </view>
            
            <!-- 提交分数表单 -->
            <view class="submit-score">
                <input class="nickname-input" 
                       placeholder="输入昵称提交分数" 
                       value="{{nickname}}" 
                       bindinput="onNicknameInput" 
                       maxlength="10"/>
                <button class="submit-btn" 
                        bindtap="submitScore" 
                        disabled="{{submitting}}">
                    {{submitting ? '提交中...' : '提交分数'}}
                </button>
            </view>
            
            <view class="modal-buttons">
                <button class="modal-btn" bindtap="newGame">再来一局</button>
                <button class="modal-btn secondary" bindtap="showRanking">查看华航排行榜</button>
            </view>
        </view>
    </view>

    <!-- 排行榜弹窗 -->
    <view wx:if="{{showRankingModal}}" class="ranking-modal">
        <view class="modal-content">
            <view class="modal-title">🏆 华航排行榜</view>
            
            <view wx:if="{{loadingRanking}}" class="loading">
                <text class="loading-text">加载中...</text>
            </view>
            
            <view wx:elif="{{rankingList.length > 0}}" class="ranking-list">
                <!-- 显示“我的排名” -->
                <view wx:if="{{currentUserRank > 0}}" class="ranking-item top-three">
                    <view class="rank-number">
                        <text>{{currentUserRank}}</text>
                    </view>
                    <view class="rank-info">
                        <text class="rank-nickname">我的排名</text>
                        <view class="rank-details">
                            <text class="rank-score">{{currentUserScore}}分</text>
                            <text class="rank-moves">--步</text>
                        </view>
                    </view>
                    <text class="rank-time">刚刚</text>
                </view>

                <!-- 排行榜列表 -->
                <view wx:for="{{rankingList}}"
                      wx:key="id"
                      class="ranking-item {{index < 3 ? 'top-three' : ''}}">
                    <view class="rank-number">
                        <text wx:if="{{index === 0}}">🥇</text>
                        <text wx:elif="{{index === 1}}">🥈</text>
                        <text wx:elif="{{index === 2}}">🥉</text>
                        <text wx:else>{{index + 1}}</text>
                    </view>
                    <view class="rank-info">
                        <text class="rank-nickname">{{item.nickname}}</text>
                        <view class="rank-details">
                            <text class="rank-score">{{item.score}}分</text>
                            <text class="rank-moves">{{item.moves}}步</text>
                        </view>
                    </view>
                    <text class="rank-time">{{item.timeAgo}}</text>
                </view>
            </view>
            
            <view wx:else class="empty-ranking">
                <text class="empty-text">暂无排行数据</text>
                <text class="empty-hint">快来创造第一个记录吧！</text>
            </view>
            
            <button class="modal-btn" bindtap="closeRanking">关闭</button>
        </view>
    </view>
</view>
