// 用户管理工具类
class UserManager {
    constructor() {
        this.userInfo = null;
        this.isLoggedIn = false;
        this.loginCallbacks = [];
    }

    // 初始化用户管理器
    init() {
        this.loadUserInfo();
    }

    // 从本地存储加载用户信息
    loadUserInfo() {
        try {
            const userInfo = wx.getStorageSync('user_info');
            const loginStatus = wx.getStorageSync('user_login_status');
            
            if (userInfo && loginStatus) {
                this.userInfo = userInfo;
                this.isLoggedIn = true;
                console.log('用户信息加载成功:', this.userInfo);
            } else {
                console.log('未找到用户登录信息');
            }
        } catch (e) {
            console.error('加载用户信息失败:', e);
        }
    }

    // 保存用户信息到本地存储
    saveUserInfo(userInfo) {
        try {
            wx.setStorageSync('user_info', userInfo);
            wx.setStorageSync('user_login_status', true);
            this.userInfo = userInfo;
            this.isLoggedIn = true;
            console.log('用户信息保存成功:', userInfo);
        } catch (e) {
            console.error('保存用户信息失败:', e);
        }
    }

    // 微信登录
    async wxLogin() {
        return new Promise((resolve, reject) => {
            wx.login({
                success: (res) => {
                    if (res.code) {
                        console.log('微信登录成功，code:', res.code);
                        resolve(res.code);
                    } else {
                        console.error('微信登录失败:', res.errMsg);
                        reject(new Error(res.errMsg));
                    }
                },
                fail: (err) => {
                    console.error('微信登录失败:', err);
                    reject(err);
                }
            });
        });
    }

    // 获取用户信息（头像和昵称）
    async getUserProfile() {
        return new Promise((resolve, reject) => {
            wx.getUserProfile({
                desc: '用于完善资料及展示头像昵称',
                success: (res) => {
                    console.log('获取用户信息成功:', res.userInfo);
                    resolve(res.userInfo);
                },
                fail: (err) => {
                    console.error('获取用户信息失败:', err);
                    reject(err);
                }
            });
        });
    }

    // 完整的登录流程 - 必须由用户直接点击触发
    loginWithUserGesture() {
        return new Promise((resolve, reject) => {
            wx.showLoading({
                title: '登录中...',
                mask: true
            });

            // 1. 先获取用户信息（必须在用户点击事件中直接调用）
            wx.getUserProfile({
                desc: '用于完善资料及展示头像昵称',
                success: (userProfileRes) => {
                    // 2. 然后获取微信登录code
                    wx.login({
                        success: (loginRes) => {
                            if (loginRes.code) {
                                // 3. 组合用户数据
                                const userData = {
                                    code: loginRes.code,
                                    nickName: userProfileRes.userInfo.nickName,
                                    avatarUrl: userProfileRes.userInfo.avatarUrl,
                                    gender: userProfileRes.userInfo.gender,
                                    country: userProfileRes.userInfo.country,
                                    province: userProfileRes.userInfo.province,
                                    city: userProfileRes.userInfo.city,
                                    language: userProfileRes.userInfo.language,
                                    loginTime: new Date().toISOString()
                                };

                                // 4. 保存用户信息
                                this.saveUserInfo(userData);

                                // 5. 通知登录成功
                                this.notifyLoginSuccess(userData);

                                wx.hideLoading();
                                wx.showToast({
                                    title: '登录成功',
                                    icon: 'success'
                                });

                                resolve(userData);
                            } else {
                                wx.hideLoading();
                                wx.showToast({
                                    title: '微信登录失败',
                                    icon: 'none'
                                });
                                reject(new Error('微信登录失败'));
                            }
                        },
                        fail: (loginErr) => {
                            wx.hideLoading();
                            console.error('微信登录失败:', loginErr);
                            wx.showToast({
                                title: '微信登录失败',
                                icon: 'none'
                            });
                            reject(loginErr);
                        }
                    });
                },
                fail: (profileErr) => {
                    wx.hideLoading();
                    console.error('获取用户信息失败:', profileErr);

                    let errorMsg = '获取用户信息失败';
                    if (profileErr.errMsg) {
                        if (profileErr.errMsg.includes('auth deny')) {
                            errorMsg = '用户拒绝授权';
                        } else if (profileErr.errMsg.includes('can only be invoked by user TAP gesture')) {
                            errorMsg = '请直接点击登录按钮';
                        }
                    }

                    wx.showToast({
                        title: errorMsg,
                        icon: 'none',
                        duration: 2000
                    });

                    reject(profileErr);
                }
            });
        });
    }

    // 退出登录
    logout() {
        try {
            wx.removeStorageSync('user_info');
            wx.removeStorageSync('user_login_status');
            this.userInfo = null;
            this.isLoggedIn = false;
            
            wx.showToast({
                title: '已退出登录',
                icon: 'success'
            });

            console.log('用户已退出登录');
        } catch (e) {
            console.error('退出登录失败:', e);
        }
    }

    // 检查登录状态
    checkLoginStatus() {
        return this.isLoggedIn && this.userInfo;
    }

    // 获取用户昵称
    getNickname() {
        return this.userInfo ? this.userInfo.nickName : '';
    }

    // 获取用户头像
    getAvatarUrl() {
        return this.userInfo ? this.userInfo.avatarUrl : '';
    }

    // 获取完整用户信息
    getUserInfo() {
        return this.userInfo;
    }

    // 添加登录成功回调
    onLoginSuccess(callback) {
        this.loginCallbacks.push(callback);
    }

    // 通知登录成功
    notifyLoginSuccess(userInfo) {
        this.loginCallbacks.forEach(callback => {
            try {
                callback(userInfo);
            } catch (e) {
                console.error('登录回调执行失败:', e);
            }
        });
    }

    // 强制登录检查
    async requireLogin() {
        if (this.checkLoginStatus()) {
            return this.userInfo;
        }

        return new Promise((resolve, reject) => {
            wx.showModal({
                title: '需要登录',
                content: '使用此功能需要先登录，是否立即登录？',
                confirmText: '立即登录',
                cancelText: '取消',
                success: async (res) => {
                    if (res.confirm) {
                        // 直接调用登录，因为这是在用户点击确认后触发的
                        this.loginWithUserGesture()
                            .then(userInfo => resolve(userInfo))
                            .catch(error => reject(error));
                    } else {
                        reject(new Error('用户取消登录'));
                    }
                }
            });
        });
    }

    // 更新用户昵称
    updateNickname(newNickname) {
        if (this.userInfo) {
            this.userInfo.nickName = newNickname;
            this.saveUserInfo(this.userInfo);
        }
    }
}

// 创建全局用户管理器实例
const userManager = new UserManager();

module.exports = userManager;
