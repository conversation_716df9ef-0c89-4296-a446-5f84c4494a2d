const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const pool = require('./db');
const { jscode2session } = require('./wechat');

dotenv.config();
const app = express();
app.use(express.json());

// CORS
const origins = (process.env.CORS_ORIGINS || '').split(',').map(s => s.trim()).filter(Boolean);
app.use(cors({ origin: (origin, cb) => cb(null, true), credentials: true }));

app.get('/', (req, res) => res.send({ ok: true, ts: Date.now() }));

// 登录：code -> openid，并保存/更新用户
app.post('/api/game2048/login', async (req, res) => {
  try {
    const { code, wxNickName, avatarUrl } = req.body || {};
    if (!code) return res.status(400).send({ success: false, message: '缺少code' });

    const info = await jscode2session(code);
    const openid = info.openid;

    // upsert 用户（仅在提供昵称/头像时更新）
    if (openid) {
      const wxname = wxNickName || '';
      const avatar = avatarUrl || '';
      if (wxname || avatar) {
        await pool.execute(
          `INSERT INTO users (openid, wx_nickname, game_nickname, avatar_url)
           VALUES (?, ?, ?, ?)
           ON DUPLICATE KEY UPDATE wx_nickname=VALUES(wx_nickname), avatar_url=VALUES(avatar_url), updated_at=CURRENT_TIMESTAMP`,
          [openid, wxname, wxname || '玩家', avatar]
        );
      }
    }

    res.send({ success: true, data: { openid } });
  } catch (e) {
    res.status(500).send({ success: false, message: e.message });
  }
});

// 保存用户资料（游戏昵称）
app.post('/api/game2048/save-user', async (req, res) => {
  try {
    let { openid, code, wxNickName, gameNickname, avatarUrl } = req.body || {};
    if (!openid && code) {
      const info = await jscode2session(code);
      openid = info.openid;
    }
    if (!openid) return res.status(400).send({ success: false, message: '缺少openid' });

    // 默认为微信昵称
    if (!gameNickname) gameNickname = wxNickName || '玩家';

    await pool.execute(
      `INSERT INTO users (openid, wx_nickname, game_nickname, avatar_url)
       VALUES (?, ?, ?, ?)
       ON DUPLICATE KEY UPDATE wx_nickname=VALUES(wx_nickname), game_nickname=VALUES(game_nickname), avatar_url=VALUES(avatar_url), updated_at=CURRENT_TIMESTAMP`,
      [openid, wxNickName || gameNickname, gameNickname, avatarUrl || null]
    );

    res.send({ success: true });
  } catch (e) {
    res.status(500).send({ success: false, message: e.message });
  }
});

// 提交分数
app.post('/api/game2048/submit-score', async (req, res) => {
  try {
    const { score, moves, wxUserInfo } = req.body || {};
    if (typeof score !== 'number') return res.status(400).send({ success: false, message: '分数必须为数字' });

    let openid = wxUserInfo && wxUserInfo.openid;
    if (!openid && wxUserInfo && wxUserInfo.code) {
      const info = await jscode2session(wxUserInfo.code);
      openid = info.openid;
    }
    if (!openid) return res.status(400).send({ success: false, message: '缺少openid' });

    // 确保用户存在，并拿到展示昵称
    const [rows] = await pool.execute('SELECT game_nickname, wx_nickname FROM users WHERE openid=?', [openid]);
    let nickname = (rows && rows[0] && rows[0].game_nickname) || (wxUserInfo && wxUserInfo.nickName) || '玩家';
    if (!rows || !rows.length) {
      await pool.execute(
        `INSERT INTO users (openid, wx_nickname, game_nickname, avatar_url)
         VALUES (?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE updated_at=CURRENT_TIMESTAMP`,
        [openid, wxUserInfo && wxUserInfo.nickName || '玩家', wxUserInfo && wxUserInfo.nickName || '玩家', wxUserInfo && wxUserInfo.avatarUrl || null]
      );
    }

    await pool.execute(
      'INSERT INTO scores (openid, nickname, score, moves) VALUES (?, ?, ?, ?)',
      [openid, nickname, score || 0, moves || 0]
    );

    res.send({ success: true });
  } catch (e) {
    res.status(500).send({ success: false, message: e.message });
  }
});

// 排行榜（每用户最高分）
app.get('/api/game2048/ranking', async (req, res) => {
  try {
    const limit = Math.min(parseInt(req.query.limit || '50', 10), 100);
    const [rows] = await pool.execute(
      `SELECT s1.openid, COALESCE(u.game_nickname, '匿名用户') AS nickname, s1.score, s1.moves, s1.created_at
       FROM scores s1
       JOIN (
         SELECT openid, MAX(score) AS max_score FROM scores GROUP BY openid
       ) t ON s1.openid=t.openid AND s1.score=t.max_score
       LEFT JOIN users u ON u.openid=s1.openid
       ORDER BY s1.score DESC, s1.moves ASC, s1.created_at ASC
       LIMIT ?`,
      [limit]
    );
    res.send(rows);
  } catch (e) {
    res.status(500).send({ success: false, message: e.message, data: [] });
  }
});

// 我的排名（基于每用户最高分）
app.get('/api/game2048/my-rank', async (req, res) => {
  try {
    const { openid } = req.query;
    if (!openid) return res.status(400).send({ success: false, message: '缺少openid' });

    const [r] = await pool.execute(
      `SELECT 1 + (
         SELECT COUNT(*) FROM (
           SELECT openid, MAX(score) AS score FROM scores GROUP BY openid
         ) x WHERE x.score > my.score
       ) AS my_rank
       FROM (
         SELECT MAX(score) AS score FROM scores WHERE openid = ?
       ) my`,
      [openid]
    );

    const my_rank = r && r[0] && r[0].my_rank || 0;
    res.send({ success: true, data: { rank: my_rank } });
  } catch (e) {
    res.status(500).send({ success: false, message: e.message });
  }
});

const PORT = process.env.PORT || 4000;
app.listen(PORT, () => console.log(`API started on :${PORT}`));

