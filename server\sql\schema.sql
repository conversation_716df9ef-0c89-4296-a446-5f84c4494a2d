-- 创建 users 表
CREATE TABLE IF NOT EXISTS `users` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `openid` VARCHAR(64) NOT NULL COMMENT '微信openid',
  `unionid` VARCHAR(64) DEFAULT NULL COMMENT '微信unionid(可选)',
  `wx_nickname` VARCHAR(64) NOT NULL COMMENT '微信昵称',
  `game_nickname` VARCHAR(64) NOT NULL COMMENT '游戏昵称(默认=微信昵称，可修改)',
  `avatar_url` VARCHAR(512) DEFAULT NULL COMMENT '头像URL',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息';

-- 创建 scores 表
CREATE TABLE IF NOT EXISTS `scores` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `openid` VARCHAR(64) NOT NULL COMMENT '关联用户openid',
  `nickname` VARCHAR(64) NOT NULL COMMENT '用于展示的昵称(建议=users.game_nickname)',
  `score` INT NOT NULL DEFAULT 0 COMMENT '分数',
  `moves` INT NOT NULL DEFAULT 0 COMMENT '步数',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY `idx_score_desc` (`score` DESC),
  KEY `idx_moves` (`moves`),
  KEY `idx_openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='2048分数记录';

-- 排行榜（每用户最高分）示例查询
-- 供后端 /ranking 使用
SELECT
  s1.openid,
  COALESCE(u.game_nickname, '匿名用户') AS nickname,
  s1.score,
  s1.moves,
  s1.created_at
FROM scores s1
JOIN (
  SELECT openid, MAX(score) AS max_score
  FROM scores
  GROUP BY openid
) t ON s1.openid = t.openid AND s1.score = t.max_score
LEFT JOIN users u ON u.openid = s1.openid
ORDER BY s1.score DESC, s1.moves ASC, s1.created_at ASC
LIMIT 50;

-- 我的排名（基于每用户最高分）示例查询
-- 供后端 /my-rank 使用
SELECT 1 + (
  SELECT COUNT(*) FROM (
    SELECT openid, MAX(score) AS score FROM scores GROUP BY openid
  ) x
  WHERE x.score > my.score
) AS my_rank
FROM (
  SELECT MAX(score) AS score FROM scores WHERE openid = ?
) my;

