/* 我的页面样式 */

.container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 头部背景 */
.header-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx 60rpx 30rpx;
  position: relative;
}

.header-bg::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 0;
  right: 0;
  height: 40rpx;
  background: #f5f5f5;
  border-radius: 20rpx 20rpx 0 0;
}

.header-content {
  position: relative;
  z-index: 1;
}

/* 未登录状态 */
.login-section {
  text-align: center;
  color: white;
}

.avatar-placeholder {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20rpx auto;
}

.avatar-icon {
  font-size: 60rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-text {
  display: block;
  font-size: 32rpx;
  margin-bottom: 30rpx;
  opacity: 0.9;
}

.login-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  backdrop-filter: blur(10rpx);
}

.login-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

/* 已登录状态 */
.user-section {
  display: flex;
  align-items: center;
  color: white;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 30rpx;
}

.user-info {
  flex: 1;
}

.user-nickname {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-shadow: 1rpx 1rpx 2rpx rgba(0,0,0,0.3);
}

.user-desc {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

/* 统计区域 */
.stats-section {
  background: white;
  margin: 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 16rpx;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 10rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 菜单区域 */
.menu-section {
  background: white;
  margin: 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.menu-list {
  margin-top: 20rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9fa;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 20rpx;
}

.menu-content {
  flex: 1;
}

.menu-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.menu-desc {
  display: block;
  font-size: 22rpx;
  color: #999;
}

.menu-arrow {
  font-size: 24rpx;
  color: #ccc;
}

/* 退出登录 */
.logout-section {
  margin: 30rpx;
}

.logout-btn {
  width: 100%;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 28rpx;
}

.logout-btn:active {
  background: #ff3838;
}

/* 底部信息 */
.footer {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

.footer-text {
  display: block;
  font-size: 22rpx;
  margin-bottom: 10rpx;
}

/* 编辑弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 600rpx;
  max-width: 90%;
  overflow: hidden;
}

.modal-header {
  padding: 40rpx 30rpx 20rpx 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-body {
  padding: 30rpx;
}

.nickname-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.nickname-input:focus {
  border-color: #667eea;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 30rpx;
  border: none;
  font-size: 28rpx;
  background: white;
}

.modal-btn.cancel {
  color: #999;
  border-right: 1rpx solid #f0f0f0;
}

.modal-btn.confirm {
  color: #667eea;
  font-weight: bold;
}

.modal-btn:active {
  background: #f8f9fa;
}
