# 🔧 华航2048小游戏配置说明

## 📋 必要配置步骤

### 1. 修改云开发环境ID

**文件位置：** `app.js`

```javascript
wx.cloud.init({
    env: 'your-cloud-env-id', // 🔴 请替换为你的云开发环境ID
    traceUser: true
});
```

**获取方式：**
1. 在微信开发者工具中点击"云开发"
2. 创建新环境或使用现有环境
3. 复制环境ID（格式如：cloud1-xxx）

### 2. 修改小程序AppID

**文件位置：** `project.config.json`

```json
{
    "appid": "wxbd2a4621e92523eb", // 🔴 请替换为你的小程序AppID
    "projectname": "huahangxiaoyouxi"
}
```

**获取方式：**
1. 登录微信公众平台
2. 进入小程序管理后台
3. 在"设置"->"基本设置"中查看AppID

### 3. 部署云函数

**步骤：**
1. 右键点击 `cloudfunctions/getRanking` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 右键点击 `cloudfunctions/submitScore` 文件夹
4. 选择"上传并部署：云端安装依赖"

### 4. 创建数据库集合

**在云开发控制台中：**
1. 点击"数据库"
2. 创建集合：`game2048_scores`
3. 权限设置：
   - 读权限：所有用户可读
   - 写权限：仅创建者可写

## 🎵 音效文件说明

当前音效文件：
- ✅ `move.mp3` - 移动音效
- ✅ `newgame.mp3` - 新游戏音效
- ✅ `gameover.mp3` - 游戏结束音效
- ✅ `win.mp3` - 胜利音效（临时使用newgame.mp3）
- ✅ `click-ji.mp3` - 点击音效1
- ✅ `click-ni.mp3` - 点击音效2
- ✅ `click-tai.mp3` - 点击音效3
- ✅ `click-mei.mp3` - 点击音效4
- ✅ `jinitaimei.mp3` - 排行榜音效

**注意：** win.mp3 目前是newgame.mp3的副本，建议替换为专门的胜利音效。

## 🚀 测试步骤

### 1. 基础功能测试
- [ ] 游戏可以正常启动
- [ ] 方块可以正常移动和合并
- [ ] 分数计算正确
- [ ] 音效播放正常

### 2. 排行榜功能测试
- [ ] 可以提交分数
- [ ] 可以查看排行榜
- [ ] 排名计算正确
- [ ] 防作弊机制生效

### 3. 云函数测试
- [ ] getRanking云函数正常
- [ ] submitScore云函数正常
- [ ] 数据库读写正常

## ⚠️ 常见问题

### Q1: 云函数调用失败
**解决方案：**
1. 检查云开发环境ID是否正确
2. 确认云函数已正确部署
3. 检查网络连接

### Q2: 排行榜显示空白
**解决方案：**
1. 检查数据库集合是否创建
2. 确认数据库权限设置正确
3. 先提交一个分数测试

### Q3: 音效无法播放
**解决方案：**
1. 检查音效文件是否存在
2. 确认文件路径正确
3. 检查音效开关状态

### Q4: 小程序无法预览
**解决方案：**
1. 检查AppID是否正确
2. 确认开发者权限
3. 检查项目配置文件

## 📱 发布前检查清单

- [ ] 云开发环境ID已正确配置
- [ ] 小程序AppID已正确配置
- [ ] 云函数已成功部署
- [ ] 数据库集合已创建并设置权限
- [ ] 游戏功能测试通过
- [ ] 排行榜功能测试通过
- [ ] 音效播放正常
- [ ] 项目信息已更新（名称、描述等）

## 🔄 更新音效文件

如需更换音效文件：
1. 将新的音效文件放入 `static/audio/` 目录
2. 确保文件名与代码中的路径一致
3. 建议文件大小控制在50KB以内
4. 支持格式：MP3, AAC, WAV

## 📞 技术支持

如遇到问题，请检查：
1. 微信开发者工具控制台错误信息
2. 云开发控制台日志
3. 网络连接状态

---

**配置完成后，你的华航2048小游戏就可以独立运行了！** 🎮✨
