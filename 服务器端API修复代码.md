# 🔧 服务器端API修复代码

## 🚨 问题确认

根据日志显示，API始终返回这两条固定数据：
- 昵称"你好"，分数6200，步数438，时间"2025-07-28T16:09:00.000Z"
- 昵称"测试用户"，分数2048，步数150，时间"2025-07-31T02:23:46.000Z"

这明显是硬编码的测试数据，不是数据库中的真实数据。

## 🔍 问题定位

### 可能的错误代码示例：

```javascript
// ❌ 错误：硬编码测试数据
app.get('/api/game2048/ranking', (req, res) => {
    const testData = [
        {
            id: 1,
            nickname: "你好",
            score: 6200,
            moves: 438,
            timestamp: "2025-07-28T16:09:00.000Z"
        },
        {
            id: 2,
            nickname: "测试用户", 
            score: 2048,
            moves: 150,
            timestamp: "2025-07-31T02:23:46.000Z"
        }
    ];
    
    res.json(testData); // 直接返回测试数据
});
```

## ✅ 正确的API实现

### Node.js + MySQL 示例：

```javascript
const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
    host: 'your-database-host',
    user: 'your-username',
    password: 'your-password',
    database: 'game2048',
    charset: 'utf8mb4'
};

// 排行榜API - 正确实现
app.get('/api/game2048/ranking', async (req, res) => {
    let connection;
    
    try {
        console.log('=== 排行榜API调用 ===');
        console.log('请求参数:', req.query);
        
        // 获取参数
        const limit = parseInt(req.query.limit) || 50;
        
        // 连接数据库
        connection = await mysql.createConnection(dbConfig);
        
        // 查询数据库
        const [rows] = await connection.execute(
            `SELECT 
                id,
                nickname,
                score,
                moves,
                created_at as timestamp
            FROM scores 
            ORDER BY score DESC 
            LIMIT ?`,
            [limit]
        );
        
        console.log('数据库查询结果数量:', rows.length);
        console.log('前3条数据:', rows.slice(0, 3));
        
        // 返回数据
        res.json(rows);
        
    } catch (error) {
        console.error('排行榜API错误:', error);
        res.status(500).json({ 
            error: '服务器错误',
            message: error.message 
        });
    } finally {
        if (connection) {
            await connection.end();
        }
    }
});
```

### Express + Sequelize 示例：

```javascript
const { Score } = require('./models'); // Sequelize模型

app.get('/api/game2048/ranking', async (req, res) => {
    try {
        console.log('=== 排行榜API调用 ===');
        
        const limit = parseInt(req.query.limit) || 50;
        
        const scores = await Score.findAll({
            attributes: ['id', 'nickname', 'score', 'moves', 'createdAt'],
            order: [['score', 'DESC']],
            limit: limit
        });
        
        console.log('查询到记录数:', scores.length);
        
        // 格式化数据
        const formattedScores = scores.map(score => ({
            id: score.id,
            nickname: score.nickname,
            score: score.score,
            moves: score.moves,
            timestamp: score.createdAt
        }));
        
        res.json(formattedScores);
        
    } catch (error) {
        console.error('排行榜查询错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});
```

## 🔧 调试步骤

### 1. 检查数据库内容

```sql
-- 连接到您的数据库
USE game2048;

-- 查看表结构
DESCRIBE scores;

-- 查看所有数据
SELECT * FROM scores ORDER BY score DESC LIMIT 20;

-- 统计记录数
SELECT COUNT(*) as total_records FROM scores;

-- 查看最近的记录
SELECT * FROM scores ORDER BY created_at DESC LIMIT 10;
```

### 2. 添加调试日志

```javascript
app.get('/api/game2048/ranking', async (req, res) => {
    console.log('🔍 排行榜API调用开始');
    console.log('📋 请求参数:', req.query);
    console.log('🕐 请求时间:', new Date().toISOString());
    
    try {
        // 数据库查询
        const results = await db.query(sql, params);
        
        console.log('📊 数据库查询完成');
        console.log('📈 查询结果数量:', results.length);
        console.log('📝 前3条数据:', JSON.stringify(results.slice(0, 3), null, 2));
        
        res.json(results);
        
    } catch (error) {
        console.error('❌ 查询错误:', error);
        res.status(500).json({ error: error.message });
    }
});
```

### 3. 测试API端点

```bash
# 直接测试API
curl "https://api.huahang.me/api/game2048/ranking?limit=10"

# 或者在浏览器中访问
https://api.huahang.me/api/game2048/ranking?limit=10
```

## 🚀 立即修复步骤

### 第一步：找到API文件
在您的服务器代码中找到处理 `/api/game2048/ranking` 的文件

### 第二步：检查是否硬编码
搜索代码中是否有：
- `"你好"`
- `6200`
- `"测试用户"`
- `2048`

### 第三步：替换为数据库查询
将硬编码的测试数据替换为真实的数据库查询

### 第四步：添加调试日志
添加console.log来查看实际的查询结果

### 第五步：重启服务器
修改代码后重启服务器应用

## 📋 检查清单

- [ ] 找到了API处理文件
- [ ] 确认没有硬编码测试数据
- [ ] 数据库连接配置正确
- [ ] SQL查询语句正确
- [ ] 添加了调试日志
- [ ] 重启了服务器
- [ ] 测试API返回真实数据

## 🆘 如果仍有问题

请提供以下信息：
1. **服务器端API代码**（处理ranking的部分）
2. **数据库直接查询结果**
3. **服务器日志输出**
4. **API直接访问结果**（浏览器访问API地址）

---

**🎯 目标：让API返回game2048.scores表中的真实数据，而不是固定的测试数据！**
