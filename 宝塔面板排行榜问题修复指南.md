# 🔧 宝塔面板排行榜问题修复指南

## 🎯 目标
在宝塔面板中检查和修复API硬编码测试数据的问题，让排行榜显示阿里云数据库中的真实数据。

## 📋 第一步：登录宝塔面板

1. **打开浏览器**，访问您的宝塔面板地址：
   ```
   http://您的服务器IP:8888
   ```

2. **输入用户名和密码**登录宝塔面板

## 🔍 第二步：找到项目文件

### 方法1：通过文件管理器
1. 点击左侧菜单 **"文件"**
2. 进入您的网站根目录，通常是：
   - `/www/wwwroot/您的域名/`
   - 或 `/www/wwwroot/api.huahang.me/`
   - 或 `/root/您的项目名/`

### 方法2：通过网站管理
1. 点击左侧菜单 **"网站"**
2. 找到您的API网站
3. 点击 **"根目录"** 进入文件管理

## 🔎 第三步：搜索硬编码的测试数据

### 在宝塔文件管理器中搜索：

1. **进入项目根目录**
2. **点击右上角的搜索图标** 🔍
3. **搜索关键词**，逐一搜索以下内容：
   - `你好`
   - `6200`
   - `测试用户`
   - `2048`
   - `ranking`
   - `api/game2048/ranking`

### 重点检查的文件类型：
- `*.js` - JavaScript文件
- `*.ts` - TypeScript文件
- `*.php` - PHP文件
- `app.js`, `server.js`, `index.js` - 主程序文件
- `routes/` 文件夹下的路由文件
- `api/` 文件夹下的API文件

## 📝 第四步：检查API代码

### 找到处理排行榜的文件后，查看代码：

**❌ 错误的硬编码示例：**
```javascript
// 这种代码需要修复
app.get('/api/game2048/ranking', (req, res) => {
    const testData = [
        {
            id: 1,
            nickname: "你好",
            score: 6200,
            moves: 438,
            timestamp: "2025-07-28T16:09:00.000Z"
        },
        {
            id: 2,
            nickname: "测试用户",
            score: 2048,
            moves: 150,
            timestamp: "2025-07-31T02:23:46.000Z"
        }
    ];
    
    res.json(testData); // 直接返回测试数据
});
```

**✅ 正确的数据库查询代码：**
```javascript
// 修复后的代码
app.get('/api/game2048/ranking', async (req, res) => {
    try {
        const limit = req.query.limit || 50;
        
        // 查询阿里云数据库
        const query = `
            SELECT id, nickname, score, moves, created_at as timestamp 
            FROM scores 
            ORDER BY score DESC 
            LIMIT ?
        `;
        
        const results = await db.query(query, [limit]);
        console.log('从数据库查询到', results.length, '条记录');
        
        res.json(results);
    } catch (error) {
        console.error('数据库查询错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});
```

## 🛠️ 第五步：修复API代码

### 在宝塔面板中编辑文件：

1. **找到包含硬编码数据的文件**
2. **点击文件名**打开文件
3. **点击"编辑"按钮**
4. **删除硬编码的测试数据**
5. **替换为数据库查询代码**
6. **点击"保存"**

### 修复要点：
- 移除所有包含 `"你好"`, `6200`, `"测试用户"`, `2048` 的硬编码数据
- 确保使用正确的数据库连接
- 添加错误处理
- 添加调试日志

## 🗄️ 第六步：检查数据库连接

### 在宝塔面板中检查数据库：

1. **点击左侧菜单"数据库"**
2. **找到game2048数据库**
3. **点击"管理"进入phpMyAdmin**
4. **执行SQL查询**：
   ```sql
   USE game2048;
   SELECT * FROM scores ORDER BY score DESC LIMIT 10;
   SELECT COUNT(*) as total FROM scores;
   ```

### 确认数据库配置：
检查API代码中的数据库连接配置是否正确：
```javascript
const dbConfig = {
    host: 'localhost',  // 或您的阿里云RDS地址
    user: 'game2048',   // 数据库用户名
    password: 'your-password',
    database: 'game2048',
    port: 3306
};
```

## 🔄 第七步：重启服务

### 在宝塔面板中重启应用：

#### 如果是Node.js应用：
1. 点击左侧菜单 **"软件商店"**
2. 找到 **"PM2管理器"**
3. 点击 **"设置"**
4. 找到您的应用，点击 **"重启"**

#### 如果是PHP应用：
1. 点击左侧菜单 **"网站"**
2. 找到您的网站，点击 **"设置"**
3. 点击 **"PHP版本"**，重新选择PHP版本来重启

#### 通用方法：
1. 点击左侧菜单 **"终端"**
2. 执行重启命令：
   ```bash
   # 重启PM2应用
   pm2 restart all
   
   # 或重启特定应用
   pm2 restart your-app-name
   
   # 重启Nginx
   nginx -s reload
   ```

## 🧪 第八步：测试验证

### 在宝塔面板中测试API：

1. **点击左侧菜单"终端"**
2. **执行测试命令**：
   ```bash
   curl "https://api.huahang.me/api/game2048/ranking?limit=10"
   ```

3. **查看返回结果**，确认不再是固定的测试数据

### 在小程序中测试：
1. 进入2048游戏页面
2. 点击"🔧 测试API"
3. 选择"测试排行榜API"
4. 查看是否返回真实数据

## 📊 第九步：查看日志

### 在宝塔面板中查看日志：

1. **点击左侧菜单"文件"**
2. **进入日志目录**：
   - `/www/wwwroot/您的网站/logs/`
   - 或 `/var/log/`
3. **查看应用日志**：
   - `access.log` - 访问日志
   - `error.log` - 错误日志
   - `app.log` - 应用日志

## 📋 检查清单

请逐一确认：

- [ ] 已登录宝塔面板
- [ ] 找到了项目文件位置
- [ ] 搜索到了包含硬编码数据的文件
- [ ] 修改了API代码，移除硬编码数据
- [ ] 确认数据库连接配置正确
- [ ] 在数据库中确认有真实数据
- [ ] 重启了应用服务
- [ ] 测试API返回真实数据
- [ ] 小程序排行榜显示正确

## 🚨 常见问题

### 问题1：找不到包含硬编码数据的文件
**解决方案：** 
- 扩大搜索范围，搜索整个服务器
- 检查是否有多个项目或备份文件
- 查看Nginx配置，确认API指向的正确目录

### 问题2：修改代码后仍返回测试数据
**解决方案：**
- 确认修改的是正确的文件
- 清除浏览器缓存
- 重启服务器
- 检查是否有负载均衡或CDN缓存

### 问题3：数据库连接失败
**解决方案：**
- 检查数据库用户名密码
- 确认数据库服务正在运行
- 检查防火墙设置
- 验证数据库权限

---

**🎯 完成以上步骤后，您的排行榜应该能显示阿里云数据库中的真实数据了！**
