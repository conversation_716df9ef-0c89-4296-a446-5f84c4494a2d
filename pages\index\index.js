// pages/index/index.js
Page({
  data: {
    // 2048游戏统计数据
    game2048Stats: {
      bestScore: 0,
      playCount: 0
    }
  },

  onLoad() {
    console.log('华航小游戏首页加载');
    this.loadGameStats();
  },

  onShow() {
    // 每次显示页面时刷新统计数据
    this.loadGameStats();
  },

  // 加载游戏统计数据
  loadGameStats() {
    try {
      // 加载2048游戏统计
      const bestScore = wx.getStorageSync('game2048_best_score') || 0;
      const playCount = wx.getStorageSync('game2048_play_count') || 0;
      
      this.setData({
        'game2048Stats.bestScore': bestScore,
        'game2048Stats.playCount': playCount
      });
    } catch (e) {
      console.error('加载游戏统计失败:', e);
    }
  },

  // 导航到游戏页面
  navigateToGame(e) {
    const gameType = e.currentTarget.dataset.game;
    
    switch (gameType) {
      case '2048':
        // 增加游戏次数统计
        this.incrementPlayCount();
        
        wx.navigateTo({
          url: '/pages/game2048/game2048',
          success: () => {
            console.log('导航到2048游戏成功');
          },
          fail: (err) => {
            console.error('导航到2048游戏失败:', err);
            wx.showToast({
              title: '游戏加载失败',
              icon: 'none'
            });
          }
        });
        break;
      
      default:
        wx.showToast({
          title: '游戏即将推出，敬请期待！',
          icon: 'none',
          duration: 2000
        });
        break;
    }
  },

  // 增加游戏次数统计
  incrementPlayCount() {
    try {
      const currentCount = wx.getStorageSync('game2048_play_count') || 0;
      const newCount = currentCount + 1;
      wx.setStorageSync('game2048_play_count', newCount);
      
      this.setData({
        'game2048Stats.playCount': newCount
      });
    } catch (e) {
      console.error('更新游戏次数失败:', e);
    }
  },

  // goToProfile函数已移除，现在使用底部导航栏

  /* 已删除特色功能：排行榜入口、成就、交流群、设置等 */

  // 显示排行榜（已废弃，保留以兼容旧入口。如需再次使用请在WXML中添加按钮）
  showRanking() {
    wx.navigateTo({
      url: '/pages/ranking/ranking',
      fail: () => {
        // 如果排行榜页面不存在，直接跳转到2048游戏并显示排行榜
        wx.navigateTo({
          url: '/pages/game2048/game2048',
          success: () => {
            // 通过事件通知游戏页面显示排行榜
            setTimeout(() => {
              wx.showToast({
                title: '请点击"🏆 华航榜"查看排行榜',
                icon: 'none',
                duration: 3000
              });
            }, 1000);
          }
        });
      }
    });
  },

  // 显示成就系统（已废弃，入口已从首页移除）
  showAchievements() {
    wx.showModal({
      title: '成就系统',
      content: '成就系统正在开发中，将包括：\n\n🏆 分数达人\n🎯 连击高手\n⚡ 速度之王\n🎮 游戏专家\n\n敬请期待！',
      showCancel: false,
      confirmText: '期待中'
    });
  },

  // 加入交流群（已废弃，入口已从首页移除）
  joinGroup() {
    wx.showModal({
      title: '加入华航游戏交流群',
      content: '想要和其他华航同学一起交流游戏心得吗？\n\n扫描群二维码或搜索群号加入我们！\n\n群内有游戏攻略、比赛活动等精彩内容等你来！',
      confirmText: '好的',
      cancelText: '稍后',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '请联系管理员获取群信息',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  },

  // 显示设置（已废弃，入口已从首页移除）
  showSettings() {
    wx.showActionSheet({
      itemList: ['音效设置', '游戏难度', '清除数据', '关于我们'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.showSoundSettings();
            break;
          case 1:
            this.showDifficultySettings();
            break;
          case 2:
            this.showClearDataConfirm();
            break;
          case 3:
            this.showAbout();
            break;
        }
      }
    });
  },

  // 音效设置
  showSoundSettings() {
    wx.showModal({
      title: '音效设置',
      content: '音效设置请在具体游戏中进行调整。\n\n在2048游戏中点击右上角的音效按钮即可开启/关闭音效。',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 难度设置
  showDifficultySettings() {
    wx.showModal({
      title: '游戏难度',
      content: '目前游戏难度为标准模式。\n\n后续版本将推出：\n• 简单模式（3x3棋盘）\n• 困难模式（5x5棋盘）\n• 极限模式（特殊规则）',
      showCancel: false,
      confirmText: '期待'
    });
  },

  // 清除数据确认
  showClearDataConfirm() {
    wx.showModal({
      title: '清除数据',
      content: '确定要清除所有游戏数据吗？\n\n这将删除：\n• 最高分记录\n• 游戏次数统计\n• 个人设置\n\n此操作不可恢复！',
      confirmText: '确定清除',
      confirmColor: '#ff4757',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.clearAllData();
        }
      }
    });
  },

  // 清除所有数据
  clearAllData() {
    try {
      wx.removeStorageSync('game2048_best_score');
      wx.removeStorageSync('game2048_play_count');
      wx.removeStorageSync('game2048_nickname');
      wx.removeStorageSync('game2048_local_scores');
      wx.removeStorageSync('game2048_ranking_cache');
      
      // 重新加载统计数据
      this.loadGameStats();
      
      wx.showToast({
        title: '数据清除成功',
        icon: 'success'
      });
    } catch (e) {
      console.error('清除数据失败:', e);
      wx.showToast({
        title: '清除失败',
        icon: 'none'
      });
    }
  },

  // 关于我们
  showAbout() {
    wx.showModal({
      title: '关于华航小游戏',
      content: '华航小游戏 v1.0\n\n一个专为华航学子打造的休闲游戏平台，让学习之余更有趣！\n\n开发者：华航技术团队\n反馈邮箱：<EMAIL>\n\n感谢您的使用和支持！',
      showCancel: false,
      confirmText: '好的'
    });
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '华航小游戏 - 精品休闲游戏合集',
      path: '/pages/index/index',
      imageUrl: '/images/share-homepage.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '华航小游戏 - 让学习之余更有趣！',
      query: '',
      imageUrl: '/images/share-homepage.png'
    };
  }
});
