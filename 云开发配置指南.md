# 🔧 华航2048小游戏云开发配置指南

## 🚨 重要提示

**当前问题：** 分数提交成功但排行榜不显示新分数的原因是云开发环境未正确配置。

## 📋 解决方案

### 方案一：配置云开发环境（推荐）

#### 1. 开通微信云开发
1. 在微信开发者工具中打开项目
2. 点击工具栏的"云开发"按钮
3. 按照提示开通云开发服务
4. 创建一个新的云开发环境

#### 2. 获取环境ID
1. 在云开发控制台中，查看环境列表
2. 复制环境ID（格式类似：`cloud1-xxx-xxx`）

#### 3. 修改配置文件
**修改 `app.js` 文件：**
```javascript
App({
    onLaunch() {
        console.log('华航2048小游戏启动');
        
        // 初始化云开发
        if (wx.cloud) {
            wx.cloud.init({
                env: 'cloud1-xxx-xxx', // 🔴 替换为你的真实环境ID
                traceUser: true
            });
        }
    },
    
    globalData: {
        userInfo: null,
        cloudEnvId: 'cloud1-xxx-xxx' // 🔴 替换为你的真实环境ID
    }
});
```

#### 4. 创建数据库集合
1. 在云开发控制台点击"数据库"
2. 创建集合：`game2048_scores`
3. 权限设置：
   - 读权限：所有用户可读
   - 写权限：仅创建者可写

#### 5. 部署云函数
1. 右键点击 `cloudfunctions/submitScore` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 右键点击 `cloudfunctions/getRanking` 文件夹
4. 选择"上传并部署：云端安装依赖"

### 方案二：仅使用HTTP API（临时方案）

如果暂时不想配置云开发，当前代码已经自动降级到HTTP API模式。

**优点：**
- 无需配置云开发
- 立即可用

**缺点：**
- 依赖外部服务器
- 可能受网络影响

## 🧪 测试步骤

### 1. 测试分数提交
1. 玩一局2048游戏
2. 游戏结束后输入昵称
3. 点击"提交分数"
4. 观察是否显示"分数提交成功"

### 2. 测试排行榜显示
1. 点击"🏆 华航榜"按钮
2. 检查是否能看到刚才提交的分数
3. 确认排名是否正确

## 🔍 问题排查

### 如果分数提交失败：
1. 检查网络连接
2. 查看开发者工具控制台错误信息
3. 确认云开发环境ID是否正确

### 如果排行榜显示空白：
1. 确认至少提交过一次分数
2. 检查云开发数据库是否有数据
3. 查看网络请求是否成功

### 如果云函数调用失败：
1. 确认云函数已正确部署
2. 检查云开发环境权限
3. 查看云开发控制台日志

## 📞 技术支持

如果遇到问题，请检查：
1. 微信开发者工具控制台的错误信息
2. 云开发控制台的运行日志
3. 网络连接状态

---

**配置完成后，你的华航2048小游戏排行榜功能就能正常工作了！** 🎮✨
