/* pages/game2048/game2048.wxss */
page {
    height: 100%;
    overflow: hidden; /* 禁用页面滚动 */
}

.container {
    min-height: 100vh;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    padding: 20rpx;
    overflow-y: auto; /* 允许容器内滚动 */
    height: 100vh;
    box-sizing: border-box;
}

/* 游戏头部 */
.game-header {
    text-align: center;
    margin-bottom: 30rpx;
}

.game-title {
    font-size: 48rpx;
    font-weight: bold;
    color: #2d3436;
    margin-bottom: 10rpx;
}

.game-subtitle {
    font-size: 28rpx;
    color: #636e72;
}

/* 游戏信息 */
.game-info {
    display: flex;
    justify-content: space-around;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;
    padding: 25rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.info-item {
    text-align: center;
}

.info-label {
    display: block;
    font-size: 24rpx;
    color: #636e72;
    margin-bottom: 8rpx;
}

.info-value {
    font-size: 32rpx;
    font-weight: bold;
    color: #2d3436;
}

/* 游戏控制 */
.game-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10rpx;
    margin-bottom: 30rpx;
    padding: 0 10rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 30rpx;
    padding: 8rpx;
}

.control-btn {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    border: none;
    border-radius: 25rpx;
    padding: 0 15rpx;
    font-size: 24rpx;
    font-weight: 500;
    box-shadow: 0 5rpx 15rpx rgba(116, 185, 255, 0.3);
    flex: 1;
    min-width: 0;
    max-width: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1;
    height: 70rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    letter-spacing: 1rpx;
}

.control-btn:first-child {
    flex: 0.8;
    min-width: 120rpx;
}

.control-btn:nth-child(2) {
    flex: 1.2;
    min-width: 140rpx;
}

.sound-btn {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    box-shadow: 0 5rpx 15rpx rgba(253, 121, 168, 0.3);
    flex: 0.9;
    min-width: 110rpx;
}

.debug-btn {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    box-shadow: 0 5rpx 15rpx rgba(155, 89, 182, 0.3);
    flex: 0.8;
    min-width: 100rpx;
    font-size: 20rpx;
}

/* 按钮悬停效果 */
.control-btn:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

/* 小屏幕适配 */
@media screen and (max-width: 400px) {
    .game-controls {
        gap: 8rpx;
        padding: 0 5rpx;
    }

    .control-btn {
        font-size: 22rpx;
        padding: 0 12rpx;
        height: 65rpx;
    }

    .control-btn:first-child {
        min-width: 100rpx;
    }

    .control-btn:nth-child(2) {
        min-width: 120rpx;
    }

    .sound-btn {
        min-width: 95rpx;
    }
}

/* 游戏棋盘 */
.game-board {
    background: #bbada0;
    border-radius: 15rpx;
    padding: 15rpx;
    margin: 0 auto 30rpx;
    width: 600rpx;
    height: 600rpx;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
    touch-action: none; /* 禁用触摸默认行为 */
    user-select: none; /* 禁用文本选择 */
    -webkit-user-select: none;
    position: relative;
}

.board-row {
    display: flex;
    margin-bottom: 15rpx;
}

.board-row:last-child {
    margin-bottom: 0;
}

.board-cell {
    width: 130rpx;
    height: 130rpx;
    background: #cdc1b4;
    border-radius: 8rpx;
    margin-right: 15rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.15s ease-in-out;
}

.board-cell:last-child {
    margin-right: 0;
}

.cell-text {
    font-size: 48rpx;
    font-weight: bold;
    color: #776e65;
}

/* 不同数字的颜色 */
.cell-2 { background: #eee4da; }
.cell-4 { background: #ede0c8; }
.cell-8 { background: #f2b179; color: #f9f6f2; }
.cell-16 { background: #f59563; color: #f9f6f2; }
.cell-32 { background: #f67c5f; color: #f9f6f2; }
.cell-64 { background: #f65e3b; color: #f9f6f2; }
.cell-128 { background: #edcf72; color: #f9f6f2; font-size: 40rpx; }
.cell-256 { background: #edcc61; color: #f9f6f2; font-size: 40rpx; }
.cell-512 { background: #edc850; color: #f9f6f2; font-size: 40rpx; }
.cell-1024 { background: #edc53f; color: #f9f6f2; font-size: 36rpx; }
.cell-2048 { background: #edc22e; color: #f9f6f2; font-size: 36rpx; }

/* 游戏说明 */
.game-instructions {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.instruction-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #2d3436;
    margin: 25rpx 0 15rpx 0;
    text-align: center;
    border-bottom: 2rpx solid #e9ecef;
    padding-bottom: 10rpx;
    position: relative;
}

.instruction-title:first-child {
    margin-top: 0;
}

.instruction-title::before {
    content: '';
    position: absolute;
    bottom: -2rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 60rpx;
    height: 4rpx;
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    border-radius: 2rpx;
}

.instruction-list {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
    margin-bottom: 20rpx;
}

.instruction-list:last-child {
    margin-bottom: 0;
}

.instruction-item {
    font-size: 26rpx;
    color: #636e72;
    line-height: 1.6;
    padding: 5rpx 0;
    border-left: 3rpx solid transparent;
    padding-left: 15rpx;
    transition: all 0.3s ease;
}

/* 特殊样式的说明项 */
.instruction-item:nth-child(1) {
    border-left-color: #74b9ff;
}

.instruction-item:nth-child(2) {
    border-left-color: #00b894;
}

.instruction-item:nth-child(3) {
    border-left-color: #fdcb6e;
}

.instruction-item:nth-child(4) {
    border-left-color: #e17055;
}

/* 分数示例样式 */
.score-examples {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15rpx;
    padding: 20rpx;
    margin: 15rpx 0;
    border: 2rpx solid #dee2e6;
}

.example-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8rpx 0;
    border-bottom: 1rpx solid #dee2e6;
}

.example-row:last-child {
    border-bottom: none;
}

.example-merge {
    font-size: 26rpx;
    color: #495057;
    font-weight: 500;
}

.example-score {
    font-size: 26rpx;
    color: #28a745;
    font-weight: bold;
}

/* 等级卡片样式 */
.level-grid {
    display: flex;
    gap: 15rpx;
    margin: 15rpx 0;
    flex-wrap: wrap;
}

.level-card {
    flex: 1;
    min-width: 180rpx;
    background: white;
    border-radius: 15rpx;
    padding: 20rpx;
    text-align: center;
    box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
    border: 2rpx solid;
    transition: transform 0.3s ease;
}

.level-card:active {
    transform: scale(0.95);
}

.level-card.gold {
    border-color: #f39c12;
    background: linear-gradient(135deg, #fff9e6, #fef5e7);
}

.level-card.silver {
    border-color: #95a5a6;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.level-card.bronze {
    border-color: #e67e22;
    background: linear-gradient(135deg, #fdf2e9, #fbeee6);
}

.level-icon {
    font-size: 40rpx;
    margin-bottom: 8rpx;
}

.level-name {
    font-size: 28rpx;
    font-weight: bold;
    color: #2d3436;
    margin-bottom: 5rpx;
}

.level-score {
    font-size: 22rpx;
    color: #636e72;
    line-height: 1.3;
}

/* 游戏结束弹窗 */
.game-over-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 25rpx;
    padding: 40rpx;
    margin: 40rpx;
    max-width: 600rpx;
    text-align: center;
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-title {
    font-size: 40rpx;
    font-weight: bold;
    color: #2d3436;
    margin-bottom: 20rpx;
}

.modal-score {
    margin-bottom: 20rpx;
}

.score-label {
    font-size: 28rpx;
    color: #636e72;
}

.score-value {
    font-size: 36rpx;
    font-weight: bold;
    color: #e17055;
    margin-left: 10rpx;
}

.new-record {
    font-size: 32rpx;
    color: #00b894;
    margin-bottom: 20rpx;
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10rpx); }
}

/* 排名信息样式 */
.rank-info {
    margin: 20rpx 0;
    text-align: center;
}

.loading-rank {
    color: #95a5a6;
    font-size: 28rpx;
}

.current-rank {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10rpx;
}

.rank-label {
    color: #7f8c8d;
    font-size: 28rpx;
}

.rank-value {
    font-size: 32rpx;
    font-weight: bold;
}

.top-rank {
    color: #f39c12;
}

.good-rank {
    color: #27ae60;
}

.normal-rank {
    color: #3498db;
}

.rank-badge {
    font-size: 36rpx;
}

/* 提交分数 */
.submit-score {
    margin: 30rpx 0;
    text-align: center;
}

.submit-hint {
    display: block;
    font-size: 24rpx;
    color: #666;
    margin-bottom: 20rpx;
    line-height: 1.4;
}

.submit-btn {
    background: linear-gradient(135deg, #00b894, #00a085);
    color: white;
    border: none;
    border-radius: 20rpx;
    padding: 20rpx 40rpx;
    font-size: 28rpx;
    width: 100%;
}

.submit-btn[disabled] {
    background: #ddd;
    color: #999;
}

.modal-buttons {
    display: flex;
    gap: 20rpx;
    margin-top: 30rpx;
}

.modal-btn {
    flex: 1;
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    border: none;
    border-radius: 20rpx;
    padding: 20rpx;
    font-size: 28rpx;
}

.modal-btn.secondary {
    background: linear-gradient(135deg, #a29bfe, #6c5ce7);
}

/* 排行榜弹窗 */
.ranking-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.ranking-modal-content {
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    background: white;
    border-radius: 20px;
    padding: 0;
    overflow: hidden;
}

.ranking-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.ranking-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 5px;
}

.ranking-subtitle {
    font-size: 14px;
    opacity: 0.9;
}

.ranking-list {
    max-height: 50vh;
    overflow-y: auto;
    padding: 10px;
}

.ranking-item {
    display: flex;
    align-items: center;
    padding: 15px;
    margin: 8px 0;
    background: #f8f9fa;
    border-radius: 15px;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
    position: relative;
}

.ranking-item.top-three {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    border-left-color: #ffa726;
}

.rank-number {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    margin-right: 12px;
    position: relative;
}

.rank-number.rank-1 {
    background: linear-gradient(135deg, #ffd700 0%, #ffb300 100%);
}

.rank-number.rank-2 {
    background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);
}

.rank-number.rank-3 {
    background: linear-gradient(135deg, #cd7f32 0%, #b8860b 100%);
}

.rank-text {
    font-size: 14px;
}

.rank-medal {
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 16px;
}

.player-avatar {
    width: 40px;
    height: 40px;
    margin-right: 12px;
}

.avatar-img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.player-info {
    flex: 1;
}

.player-name {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
    display: block;
}

.player-details {
    display: flex;
    gap: 15px;
    margin-bottom: 3px;
}

.player-score {
    color: #667eea;
    font-weight: bold;
    font-size: 14px;
}

.player-moves {
    color: #666;
    font-size: 14px;
}

.player-time {
    color: #999;
    font-size: 12px;
}

.player-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
}

.badge-text {
    font-size: 10px;
}

.loading {
    padding: 60rpx;
}

.loading-text {
    font-size: 28rpx;
    color: #636e72;
}

.ranking-list {
    max-height: 600rpx;
    overflow-y: auto;
    margin: 20rpx 0;
}

.ranking-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    border-bottom: 1rpx solid #e9ecef;
}

.ranking-item.top-three {
    background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
    border-radius: 10rpx;
    margin-bottom: 10rpx;
    border: none;
}

.rank-number {
    width: 60rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
}

.rank-info {
    flex: 1;
    margin-left: 20rpx;
}

.rank-nickname {
    display: block;
    font-size: 28rpx;
    font-weight: bold;
    color: #2d3436;
    margin-bottom: 5rpx;
}

.rank-details {
    display: flex;
    gap: 15rpx;
}

.rank-score {
    font-size: 24rpx;
    color: #e17055;
    font-weight: bold;
}

.rank-moves {
    font-size: 22rpx;
    color: #636e72;
}

.rank-time {
    font-size: 22rpx;
    color: #999;
}

.empty-ranking {
    padding: 60rpx 20rpx;
    text-align: center;
}

.empty-text {
    display: block;
    font-size: 28rpx;
    color: #636e72;
    margin-bottom: 10rpx;
}

.empty-hint {
    font-size: 24rpx;
    color: #999;
}
