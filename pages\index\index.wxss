/* 华航小游戏首页样式 */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  box-sizing: border-box;
}

/* 头部区域 */
.header {
  text-align: center;
  padding: 40rpx 0 60rpx 0;
  color: white;
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.logo-icon {
  font-size: 80rpx;
  margin-right: 20rpx;
}

.app-title {
  font-size: 56rpx;
  font-weight: bold;
  text-shadow: 2rpx 2rpx 4rpx rgba(0,0,0,0.3);
}

.app-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  font-weight: 300;
}

/* 游戏区域 */
.games-section {
  margin-bottom: 60rpx;
}

.section-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 30rpx;
  text-align: center;
  text-shadow: 1rpx 1rpx 2rpx rgba(0,0,0,0.3);
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.game-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  position: relative;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

.game-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.2);
}

.game-card.coming-soon {
  opacity: 0.7;
  background: rgba(255, 255, 255, 0.6);
}

.game-icon {
  text-align: center;
  margin-bottom: 20rpx;
}

.game-emoji {
  font-size: 60rpx;
}

.game-info {
  text-align: center;
}

.game-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.game-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.game-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 15rpx;
}

.stat-item {
  font-size: 20rpx;
  color: #888;
  background: rgba(102, 126, 234, 0.1);
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
}

.game-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
}

.game-badge.hot {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.coming-soon-label {
  background: linear-gradient(45deg, #ffa726, #ff7043);
  color: white;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
  margin-top: 10rpx;
}

/* 功能区域 */
.features-section {
  margin-bottom: 60rpx;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.feature-item {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.feature-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.8);
}

.feature-icon {
  display: block;
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.feature-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.feature-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
}

/* 底部区域 */
.footer {
  text-align: center;
  padding: 40rpx 0;
  color: rgba(255, 255, 255, 0.8);
}

.footer-text {
  display: block;
  font-size: 24rpx;
  margin-bottom: 10rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .games-grid {
    grid-template-columns: 1fr;
  }
  
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
}
