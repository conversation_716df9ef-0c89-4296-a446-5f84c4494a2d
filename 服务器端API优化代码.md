# 🔧 服务器端API优化 - 每用户最高分

## 🎯 目标
修改服务器端API，实现每个用户只显示最高分，并支持头像显示。

## 📝 需要在服务器上执行的修改

### 第一步：修改数据库表结构（如果需要）

```bash
# 在宝塔终端中执行
mysql -u game2048 -p2073586802 -e "
USE game2048;
ALTER TABLE scores ADD COLUMN avatar_url VARCHAR(500) DEFAULT NULL AFTER openid;
DESCRIBE scores;
"
```

### 第二步：修改API代码

编辑 `/www/wwwroot/api.huahang.me/app.js` 文件，找到排行榜API部分，替换为：

```javascript
// 获取排行榜API - 每用户最高分版本
app.get('/api/game2048/ranking', async (req, res) => {
    try {
        console.log('=== 排行榜API调用 ===');
        const limit = Math.min(parseInt(req.query.limit) || 50, 100);
        
        const connection = await pool.getConnection();
        
        try {
            // 获取每个用户的最高分记录
            const [rows] = await connection.execute(
                `SELECT 
                    s1.id,
                    s1.nickname,
                    s1.score,
                    s1.moves,
                    s1.created_at as timestamp,
                    s1.avatar_url
                 FROM scores s1
                 INNER JOIN (
                     SELECT 
                         COALESCE(NULLIF(openid, ''), CONCAT('ip_', ip_address)) as user_key,
                         MAX(score) as max_score
                     FROM scores
                     GROUP BY user_key
                 ) s2 ON (
                     (s1.openid IS NOT NULL AND s1.openid != '' AND s1.openid = s2.user_key) OR
                     (s1.openid IS NULL OR s1.openid = '' AND CONCAT('ip_', s1.ip_address) = s2.user_key)
                 ) AND s1.score = s2.max_score
                 GROUP BY s2.user_key
                 ORDER BY s1.score DESC, s1.created_at ASC
                 LIMIT ?`,
                [limit]
            );
            
            console.log('查询结果数量:', rows.length);
            console.log('前3条数据:', JSON.stringify(rows.slice(0, 3), null, 2));
            
            // 格式化数据并添加排名
            const rankingList = rows.map((row, index) => ({
                id: row.id,
                nickname: row.nickname,
                score: row.score,
                moves: row.moves,
                timestamp: row.timestamp,
                avatarUrl: row.avatar_url,
                rank: index + 1
            }));
            
            // 直接返回数组
            res.json(rankingList);
            
        } finally {
            connection.release();
        }
        
    } catch (error) {
        console.error('获取排行榜错误:', error);
        res.status(500).json([]);
    }
});
```

### 第三步：修改提交分数API支持头像

在提交分数API中添加头像支持：

```javascript
// 在提交分数API中，找到INSERT语句部分，修改为：
const [result] = await connection.execute(
    `INSERT INTO scores (nickname, score, moves, game_time, avg_move_time, device_info, ip_address, openid, avatar_url)
     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    [nickname, score, moves, safeGameTime, safeAvgMoveTime, safeDeviceInfo ? JSON.stringify(safeDeviceInfo) : null, ipAddress, safeOpenid, req.body.avatarUrl || null]
);
```

### 第四步：重启服务

```bash
cd /www/wwwroot/api.huahang.me
pm2 restart api-huahang
pm2 logs api-huahang --lines 10
```

### 第五步：测试API

```bash
# 测试排行榜API
curl "https://api.huahang.me/api/game2048/ranking?limit=10"
```

## 🎯 预期结果

修改后的API应该：
1. **每个用户只显示最高分**
2. **支持头像URL字段**
3. **按分数从高到低排序**
4. **去重显示（同一用户不重复）**

---

**请在服务器上执行这些修改！**
