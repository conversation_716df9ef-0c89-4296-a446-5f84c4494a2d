# 🖼️ 头像图片说明

## 📁 需要的头像文件

请在 `images/` 目录下添加以下头像文件：

### 默认头像
- `default-avatar.png` - 默认头像 (80x80px)

### 多样化头像（根据昵称自动选择）
- `avatar1.png` - 头像1 (80x80px)
- `avatar2.png` - 头像2 (80x80px)
- `avatar3.png` - 头像3 (80x80px)
- `avatar4.png` - 头像4 (80x80px)
- `avatar5.png` - 头像5 (80x80px)

## 🎨 头像设计建议

### 风格要求
- **尺寸**: 80x80 像素
- **格式**: PNG（支持透明背景）
- **风格**: 简洁、现代、友好
- **颜色**: 明亮、温暖的色调

### 设计元素
- **头像1**: 蓝色系，圆形背景，白色图标
- **头像2**: 绿色系，圆形背景，白色图标
- **头像3**: 橙色系，圆形背景，白色图标
- **头像4**: 紫色系，圆形背景，白色图标
- **头像5**: 红色系，圆形背景，白色图标

## 🔧 临时解决方案

如果暂时没有头像文件，可以：

### 方案1：使用Emoji头像
修改 `getDefaultAvatar` 函数：
```javascript
getDefaultAvatar(nickname) {
    const emojis = ['😊', '🎮', '🚀', '⭐', '🎯'];
    if (!nickname) return '👤';
    const charCode = nickname.charCodeAt(0);
    const index = charCode % emojis.length;
    return emojis[index];
}
```

### 方案2：使用在线头像服务
```javascript
getDefaultAvatar(nickname) {
    if (!nickname) return 'https://ui-avatars.com/api/?name=User&background=667eea&color=fff&size=80';
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(nickname)}&background=667eea&color=fff&size=80`;
}
```

### 方案3：使用纯色背景
在CSS中设置默认样式：
```css
.avatar-img {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}
```

## 📋 头像获取优先级

1. **用户真实头像** - 从微信获取的头像
2. **默认头像** - 根据昵称自动选择的头像
3. **占位符** - 纯色背景或Emoji

## 🎯 实现效果

- 排行榜中每个用户都有头像显示
- 头像圆形显示，带有边框和阴影
- 根据用户昵称自动分配不同颜色的头像
- 支持真实用户头像的显示

---

**🎨 建议先使用方案2（在线头像服务）作为临时解决方案，后续再添加自定义头像文件！**
