# 🔍 宝塔面板双文件夹检查指南

## 📂 发现的文件夹结构

您的服务器上有两个相关文件夹：
- `/www/wwwroot/game2048-api/`
- `/www/wwwroot/api.huahang.me/`

## 🎯 第一步：确定哪个是真正的API服务

### 方法1：检查Nginx配置

1. **在宝塔面板中点击"网站"**
2. **找到api.huahang.me网站**
3. **点击"设置" → "配置文件"**
4. **查看root路径**：
   ```nginx
   server {
       listen 80;
       server_name api.huahang.me;
       root /www/wwwroot/api.huahang.me;  # 这里显示真正的路径
       # 或者
       root /www/wwwroot/game2048-api;
   }
   ```

### 方法2：检查PM2进程

1. **点击"软件商店" → "PM2管理器" → "设置"**
2. **查看运行中的应用**
3. **查看应用的工作目录**

### 方法3：直接测试两个文件夹

在宝塔终端中执行：
```bash
# 检查两个文件夹的内容
ls -la /www/wwwroot/game2048-api/
ls -la /www/wwwroot/api.huahang.me/

# 查看哪个文件夹有package.json或主程序文件
find /www/wwwroot/game2048-api/ -name "*.js" -o -name "package.json"
find /www/wwwroot/api.huahang.me/ -name "*.js" -o -name "package.json"
```

## 🔍 第二步：在两个文件夹中搜索硬编码数据

### 检查 game2048-api 文件夹：

1. **在宝塔文件管理器中进入** `/www/wwwroot/game2048-api/`
2. **点击搜索图标** 🔍
3. **搜索关键词**：
   - `你好`
   - `6200`
   - `测试用户`
   - `ranking`

### 检查 api.huahang.me 文件夹：

1. **在宝塔文件管理器中进入** `/www/wwwroot/api.huahang.me/`
2. **点击搜索图标** 🔍
3. **搜索相同的关键词**

## 📝 第三步：重点检查的文件

### 在每个文件夹中查找这些文件：

**主程序文件：**
- `app.js`
- `server.js`
- `index.js`
- `main.js`

**路由文件：**
- `routes/game2048.js`
- `routes/ranking.js`
- `routes/api.js`
- `api/game2048/ranking.js`

**配置文件：**
- `package.json` - 查看项目名称和启动脚本
- `config.js` - 查看配置信息

## 🔧 第四步：使用终端命令快速搜索

在宝塔终端中执行以下命令：

```bash
# 在两个文件夹中搜索硬编码数据
echo "=== 搜索 game2048-api 文件夹 ==="
grep -r "你好" /www/wwwroot/game2048-api/ 2>/dev/null
grep -r "6200" /www/wwwroot/game2048-api/ 2>/dev/null
grep -r "测试用户" /www/wwwroot/game2048-api/ 2>/dev/null

echo "=== 搜索 api.huahang.me 文件夹 ==="
grep -r "你好" /www/wwwroot/api.huahang.me/ 2>/dev/null
grep -r "6200" /www/wwwroot/api.huahang.me/ 2>/dev/null
grep -r "测试用户" /www/wwwroot/api.huahang.me/ 2>/dev/null

# 搜索ranking相关代码
echo "=== 搜索ranking API ==="
grep -r "ranking" /www/wwwroot/game2048-api/ 2>/dev/null
grep -r "ranking" /www/wwwroot/api.huahang.me/ 2>/dev/null

# 查看哪个文件夹最近有修改
echo "=== 最近修改的文件 ==="
find /www/wwwroot/game2048-api/ -name "*.js" -mtime -7 2>/dev/null
find /www/wwwroot/api.huahang.me/ -name "*.js" -mtime -7 2>/dev/null
```

## 🎯 第五步：确定真正的API文件

### 根据搜索结果判断：

1. **如果在game2048-api文件夹中找到硬编码数据**：
   - 这可能是真正的API服务
   - 修改这个文件夹中的代码

2. **如果在api.huahang.me文件夹中找到硬编码数据**：
   - 这可能是真正的API服务
   - 修改这个文件夹中的代码

3. **如果两个文件夹都有相关代码**：
   - 可能一个是备份，一个是正在运行的
   - 需要确定哪个是实际运行的服务

## 🔄 第六步：确定运行中的服务

### 检查进程：

```bash
# 查看Node.js进程
ps aux | grep node
ps aux | grep game2048
ps aux | grep api

# 查看端口占用
netstat -tlnp | grep :3000
netstat -tlnp | grep :4000
netstat -tlnp | grep :8080

# 查看PM2进程详情
pm2 list
pm2 show 0  # 查看第一个进程的详情
```

## 🛠️ 第七步：修复正确的文件

### 找到硬编码数据后：

1. **编辑包含硬编码数据的文件**
2. **删除测试数据**：
   ```javascript
   // 删除这种代码
   const testData = [
       { id: 1, nickname: "你好", score: 6200, moves: 438 },
       { id: 2, nickname: "测试用户", score: 2048, moves: 150 }
   ];
   ```

3. **替换为数据库查询**：
   ```javascript
   // 替换为这种代码
   app.get('/api/game2048/ranking', async (req, res) => {
       try {
           const limit = req.query.limit || 50;
           const query = 'SELECT * FROM scores ORDER BY score DESC LIMIT ?';
           const results = await db.query(query, [limit]);
           console.log('查询到', results.length, '条记录');
           res.json(results);
       } catch (error) {
           console.error('数据库查询错误:', error);
           res.status(500).json({ error: '服务器错误' });
       }
   });
   ```

## 🔄 第八步：重启正确的服务

### 如果修改的是game2048-api：
```bash
cd /www/wwwroot/game2048-api/
pm2 restart game2048-api
# 或者
pm2 restart all
```

### 如果修改的是api.huahang.me：
```bash
cd /www/wwwroot/api.huahang.me/
pm2 restart api
# 或者
pm2 restart all
```

## 🧪 第九步：验证修复

### 测试API：
```bash
# 测试API是否返回真实数据
curl "https://api.huahang.me/api/game2048/ranking?limit=5"

# 查看服务日志
pm2 logs
```

## 📋 快速检查清单

请按顺序执行：

1. [ ] 在终端中执行搜索命令，找到包含硬编码数据的文件
2. [ ] 确定哪个文件夹是真正运行的API服务
3. [ ] 编辑正确的文件，删除硬编码数据
4. [ ] 添加数据库查询代码
5. [ ] 重启对应的PM2服务
6. [ ] 测试API返回真实数据
7. [ ] 在小程序中验证排行榜显示正确

## 🆘 如果仍有问题

请提供以下信息：
1. **搜索命令的输出结果**
2. **pm2 list 的输出**
3. **找到的包含硬编码数据的具体文件路径**
4. **修改后的API测试结果**

---

**🎯 关键是要找到真正运行的API服务文件，然后修复其中的硬编码数据！**
