# TabBar 图标说明

## 需要的图标文件

请在 `images/` 目录下添加以下图标文件：

### 首页图标
- `tab-home.png` - 首页未选中图标 (81x81px)
- `tab-home-active.png` - 首页选中图标 (81x81px)

### 我的图标  
- `tab-profile.png` - 我的未选中图标 (81x81px)
- `tab-profile-active.png` - 我的选中图标 (81x81px)

## 图标规范

- **尺寸**: 81x81 像素
- **格式**: PNG
- **背景**: 透明
- **颜色**: 
  - 未选中: #999999 (灰色)
  - 选中: #667eea (主题蓝色)

## 临时解决方案

如果暂时没有图标文件，可以：

1. **使用emoji图标**：修改app.json中的tabBar配置，移除iconPath
2. **创建简单图标**：使用任何图片编辑工具创建简单的图标
3. **下载免费图标**：从iconfont、iconify等网站下载

## 示例配置（无图标版本）

```json
"tabBar": {
    "color": "#999999",
    "selectedColor": "#667eea", 
    "backgroundColor": "#ffffff",
    "borderStyle": "black",
    "list": [
        {
            "pagePath": "pages/index/index",
            "text": "🏠 首页"
        },
        {
            "pagePath": "pages/profile/profile", 
            "text": "👤 我的"
        }
    ]
}
```
