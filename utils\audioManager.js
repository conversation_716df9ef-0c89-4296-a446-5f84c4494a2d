// 音频管理器 - 2048游戏音效系统
class AudioManager {
    constructor() {
        this.audioContexts = new Map();
        this.soundEnabled = true;
        this.isDestroyed = false;
        this.loadSoundSetting();

        // 循环点击音效相关
        this.clickSounds = [
            '/static/audio/click-ji.mp3',
            '/static/audio/click-ni.mp3',
            '/static/audio/click-tai.mp3',
            '/static/audio/click-mei.mp3'
        ];
        this.currentClickIndex = 0;
    }

    // 加载音效设置
    loadSoundSetting() {
        try {
            const soundEnabled = wx.getStorageSync('game2048_sound_enabled');
            this.soundEnabled = soundEnabled !== false; // 默认开启
        } catch (e) {
            this.soundEnabled = true;
        }
    }

    // 保存音效设置
    saveSoundSetting() {
        try {
            wx.setStorageSync('game2048_sound_enabled', this.soundEnabled);
        } catch (e) {
            console.log('保存音效设置失败:', e);
        }
    }

    // 切换音效开关
    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        this.saveSoundSetting();
        return this.soundEnabled;
    }



    // 移动音效
    playMoveSound() {
        if (!this.soundEnabled) return;
        try {
            this.playAudioFile('/static/audio/move.mp3');
        } catch (e) {
            console.log('播放移动音效失败:', e);
        }
    }

    // 新游戏音效
    playNewGameSound() {
        if (!this.soundEnabled) return;
        try {
            this.playAudioFile('/static/audio/newgame.mp3');
        } catch (e) {
            console.log('播放新游戏音效失败:', e);
        }
    }

    // 胜利音效
    playWinSound() {
        if (!this.soundEnabled) return;
        try {
            this.playAudioFile('/static/audio/win.mp3');
        } catch (e) {
            console.log('播放胜利音效失败:', e);
        }
    }

    // 游戏结束音效
    playGameOverSound() {
        if (!this.soundEnabled) return;
        try {
            this.playAudioFile('/static/audio/gameover.mp3');
        } catch (e) {
            console.log('播放游戏结束音效失败:', e);
        }
    }

    // 循环点击音效 - ji、ni、tai、mei
    playClickSound() {
        if (!this.soundEnabled) return;

        try {
            // 播放当前索引对应的音效
            const currentSound = this.clickSounds[this.currentClickIndex];
            this.playAudioFile(currentSound);

            // 更新索引，循环播放
            this.currentClickIndex = (this.currentClickIndex + 1) % this.clickSounds.length;
        } catch (e) {
            // 静默处理错误，不输出日志
        }
    }

    // 华航排行榜音效
    playRankingSound() {
        if (!this.soundEnabled) return;

        try {
            this.playAudioFile('/static/audio/jinitaimei.mp3');
        } catch (e) {
            // 静默处理错误，不输出日志
        }
    }

    // 播放音频文件
    playAudioFile(src) {
        // 检查管理器是否已销毁，如果是则重新初始化
        if (this.isDestroyed) {
            // console.log('音频管理器已销毁，尝试重新初始化'); // 减少日志输出
            this.reinitialize();
        }

        try {
            // console.log('创建音频上下文，播放文件:', src); // 减少日志输出
            const audioContext = wx.createInnerAudioContext();

            // 检查音频上下文是否创建成功
            if (!audioContext) {
                // console.log('音频上下文创建失败'); // 减少日志输出
                return;
            }

            audioContext.src = src;
            audioContext.volume = 0.6;
            // console.log('音频上下文配置完成，准备播放'); // 减少日志输出

            // 生成唯一ID并存储音频上下文
            const audioId = Date.now() + Math.random();
            this.audioContexts.set(audioId, audioContext);

            audioContext.onPlay(() => {
                // console.log('音频播放成功:', src); // 减少日志输出
            });

            audioContext.onError((err) => {
                console.log('音频文件播放失败:', err); // 保留错误日志
                this.safeDestroyAudio(audioId);
            });

            audioContext.onEnded(() => {
                this.safeDestroyAudio(audioId);
            });

            // 检查音频上下文状态后再播放
            if (audioContext && typeof audioContext.play === 'function') {
                audioContext.play();
            } else {
                // console.log('音频上下文无效，无法播放'); // 减少日志输出
                this.safeDestroyAudio(audioId);
            }

            // 设置超时销毁，防止内存泄漏
            setTimeout(() => {
                this.safeDestroyAudio(audioId);
            }, 3000);

        } catch (e) {
            console.log('创建音频上下文失败:', e); // 保留错误日志
        }
    }

    // 安全销毁音频上下文
    safeDestroyAudio(audioId) {
        if (this.isDestroyed) {
            return; // 管理器已销毁，无需处理
        }

        try {
            const audioContext = this.audioContexts.get(audioId);
            if (audioContext && typeof audioContext.destroy === 'function') {
                // 检查音频上下文是否还有效
                if (audioContext.src !== undefined) {
                    audioContext.destroy();
                }
            }
            this.audioContexts.delete(audioId);
        } catch (e) {
            console.log('销毁音频上下文失败:', audioId, e); // 保留错误日志
            // 即使销毁失败，也要从Map中删除
            try {
                this.audioContexts.delete(audioId);
            } catch (deleteError) {
                console.log('删除音频上下文引用失败:', deleteError); // 保留错误日志
            }
        }
    }





    // 重新初始化音频管理器
    reinitialize() {
        if (this.isDestroyed) {
            // console.log('重新初始化音频管理器'); // 减少日志输出
            this.isDestroyed = false;
            this.audioContexts.clear();
            this.loadSoundSetting();
        }
    }

    // 测试音效播放
    testClickSound() {
        // console.log('=== 测试点击音效 ==='); // 减少日志输出
        // console.log('音效开关状态:', this.soundEnabled); // 减少日志输出
        // console.log('管理器销毁状态:', this.isDestroyed); // 减少日志输出
        // console.log('音频上下文数量:', this.audioContexts.size); // 减少日志输出
        this.playClickSound();
    }

    // 清理资源
    destroy() {
        if (this.isDestroyed) {
            return; // 避免重复销毁
        }

        this.isDestroyed = true;

        try {
            this.audioContexts.forEach((context, audioId) => {
                try {
                    if (context && typeof context.destroy === 'function') {
                        context.destroy();
                    }
                } catch (e) {
                    console.log('清理音频上下文失败:', audioId, e);
                }
            });
            this.audioContexts.clear();
        } catch (e) {
            console.log('清理音频管理器失败:', e); // 保留错误日志
        }
    }
}

// 创建全局音频管理器实例
const audioManager = new AudioManager();

module.exports = audioManager;
