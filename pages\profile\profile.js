// pages/profile/profile.js
const userManager = require('../../utils/userManager');

Page({
  data: {
    isLoggedIn: false,
    userInfo: null,
    gameStats: {
      totalGames: 0,
      bestScore: 0,
      totalScore: 0,
      ranking: '--'
    },
    showEditModal: false,
    editNickname: ''
  },

  onLoad() {
    console.log('我的页面加载');
    userManager.init();
    this.updateUserStatus();
    this.loadGameStats();
  },

  onShow() {
    // 每次显示页面时刷新用户状态和统计数据
    this.updateUserStatus();
    this.loadGameStats();
  },

  // 更新用户状态
  updateUserStatus() {
    const isLoggedIn = userManager.checkLoginStatus();
    const userInfo = userManager.getUserInfo();
    
    this.setData({
      isLoggedIn: isLoggedIn,
      userInfo: userInfo
    });

    console.log('用户状态更新:', { isLoggedIn, userInfo });
  },

  // 加载游戏统计数据
  loadGameStats() {
    try {
      const bestScore = wx.getStorageSync('game2048_best_score') || 0;
      const playCount = wx.getStorageSync('game2048_play_count') || 0;
      const totalScore = wx.getStorageSync('game2048_total_score') || 0;
      
      this.setData({
        'gameStats.bestScore': bestScore,
        'gameStats.totalGames': playCount,
        'gameStats.totalScore': totalScore
      });

      // 如果已登录，获取排名信息
      if (this.data.isLoggedIn) {
        this.loadUserRanking();
      }
    } catch (e) {
      console.error('加载游戏统计失败:', e);
    }
  },

  // 获取用户排名
  async loadUserRanking() {
    // 这里可以调用API获取用户在排行榜中的排名
    // 暂时使用模拟数据
    this.setData({
      'gameStats.ranking': Math.floor(Math.random() * 100) + 1
    });
  },

  // 处理登录
  handleLogin() {
    userManager.loginWithUserGesture()
      .then(() => {
        this.updateUserStatus();
        this.loadGameStats();
      })
      .catch(error => {
        console.error('登录失败:', error);
      });
  },

  // 处理退出登录
  handleLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      confirmText: '退出',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          userManager.logout();
          this.updateUserStatus();
          this.setData({
            'gameStats.ranking': '--'
          });
        }
      }
    });
  },

  // 编辑资料
  editProfile() {
    if (!this.data.isLoggedIn) {
      this.requireLogin();
      return;
    }

    this.setData({
      showEditModal: true,
      editNickname: this.data.userInfo.nickName || ''
    });
  },

  // 昵称输入
  onNicknameInput(e) {
    this.setData({
      editNickname: e.detail.value
    });
  },

  // 保存昵称
  saveNickname() {
    const newNickname = this.data.editNickname.trim();
    
    if (!newNickname) {
      wx.showToast({
        title: '昵称不能为空',
        icon: 'none'
      });
      return;
    }

    if (newNickname.length > 10) {
      wx.showToast({
        title: '昵称不能超过10个字符',
        icon: 'none'
      });
      return;
    }

    // 更新用户昵称
    userManager.updateNickname(newNickname);
    this.updateUserStatus();
    this.closeEditModal();

    wx.showToast({
      title: '昵称修改成功',
      icon: 'success'
    });
  },

  // 关闭编辑弹窗
  closeEditModal() {
    this.setData({
      showEditModal: false,
      editNickname: ''
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击弹窗内容时关闭弹窗
  },

  // 查看排行榜
  viewRanking() {
    wx.navigateTo({
      url: '/pages/game2048/game2048',
      success: () => {
        // 可以通过事件通知游戏页面显示排行榜
        setTimeout(() => {
          wx.showToast({
            title: '请点击"🏆 华航榜"查看排行榜',
            icon: 'none',
            duration: 2000
          });
        }, 500);
      }
    });
  },

  // 游戏记录
  gameHistory() {
    wx.showModal({
      title: '游戏记录',
      content: '游戏记录功能正在开发中，将包括：\n\n📊 详细游戏数据\n🏆 历史最高分\n📈 进步趋势图\n🎯 成就解锁记录\n\n敬请期待！',
      showCancel: false,
      confirmText: '期待中'
    });
  },

  // 设置
  settings() {
    wx.showActionSheet({
      itemList: ['音效设置', '通知设置', '隐私设置', '清除缓存'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.soundSettings();
            break;
          case 1:
            this.notificationSettings();
            break;
          case 2:
            this.privacySettings();
            break;
          case 3:
            this.clearCache();
            break;
        }
      }
    });
  },

  // 音效设置
  soundSettings() {
    wx.showModal({
      title: '音效设置',
      content: '音效设置请在具体游戏中进行调整。\n\n在2048游戏中点击音效按钮即可开启/关闭音效。',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 通知设置
  notificationSettings() {
    wx.showModal({
      title: '通知设置',
      content: '通知功能正在开发中，将支持：\n\n🔔 新游戏提醒\n🏆 排名变化通知\n🎉 活动消息推送\n\n敬请期待！',
      showCancel: false,
      confirmText: '期待'
    });
  },

  // 隐私设置
  privacySettings() {
    wx.showModal({
      title: '隐私设置',
      content: '我们重视您的隐私保护：\n\n✅ 仅收集必要的游戏数据\n✅ 不会泄露个人信息\n✅ 数据仅用于游戏功能\n✅ 支持随时删除数据',
      showCancel: false,
      confirmText: '了解'
    });
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除所有缓存数据吗？\n\n这将删除：\n• 游戏记录和统计\n• 本地设置\n• 临时文件\n\n用户登录信息不会被删除',
      confirmText: '确定清除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          try {
            // 清除游戏相关缓存，但保留用户登录信息
            wx.removeStorageSync('game2048_best_score');
            wx.removeStorageSync('game2048_play_count');
            wx.removeStorageSync('game2048_total_score');
            wx.removeStorageSync('game2048_local_scores');
            wx.removeStorageSync('game2048_ranking_cache');
            
            this.loadGameStats();
            
            wx.showToast({
              title: '缓存清除成功',
              icon: 'success'
            });
          } catch (e) {
            wx.showToast({
              title: '清除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 意见反馈
  feedback() {
    wx.showModal({
      title: '意见反馈',
      content: '感谢您的宝贵意见！\n\n您可以通过以下方式联系我们：\n\n📧 邮箱：<EMAIL>\n💬 微信群：点击首页"交流群"加入\n🐛 问题反馈：描述具体问题和复现步骤',
      showCancel: false,
      confirmText: '好的'
    });
  },

  // 关于我们
  about() {
    wx.showModal({
      title: '关于华航小游戏',
      content: '华航小游戏 v1.0\n\n🎮 专为华航学子打造的休闲游戏平台\n🏆 支持排行榜和成就系统\n👥 促进同学间的友好竞争\n🎯 让学习之余更有趣\n\n开发团队：华航技术小组\n更新时间：2024年8月',
      showCancel: false,
      confirmText: '好的'
    });
  },

  // 需要登录提示
  requireLogin() {
    wx.showModal({
      title: '需要登录',
      content: '使用此功能需要先登录，是否立即登录？',
      confirmText: '立即登录',
      success: (res) => {
        if (res.confirm) {
          this.handleLogin();
        }
      }
    });
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '华航小游戏 - 我的游戏中心',
      path: '/pages/index/index',
      imageUrl: '/images/share-profile.png'
    };
  }
});
