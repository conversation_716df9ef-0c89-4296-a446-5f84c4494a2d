const axios = require('axios');
const dotenv = require('dotenv');
dotenv.config();

async function jscode2session(code) {
  const appid = process.env.WECHAT_APPID;
  const secret = process.env.WECHAT_SECRET;
  const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${appid}&secret=${secret}&js_code=${code}&grant_type=authorization_code`;
  const res = await axios.get(url);
  if (res.data.errcode) {
    throw new Error(`jscode2session失败: ${res.data.errmsg}`);
  }
  return res.data; // { openid, session_key, unionid? }
}

module.exports = { jscode2session };

