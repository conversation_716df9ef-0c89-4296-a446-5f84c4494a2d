-- 在 huahangminigame 数据库中创建排行榜所需表结构（最新版）
-- 若数据库未创建：CREATE DATABASE IF NOT EXISTS `huahangminigame` DEFAULT CHARACTER SET utf8mb4;
-- 然后切换到该库执行本脚本

-- users：保存微信用户信息与“游戏昵称”
CREATE TABLE IF NOT EXISTS `users` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `openid` VARCHAR(64) NOT NULL COMMENT '微信openid',
  `unionid` VARCHAR(64) DEFAULT NULL COMMENT '微信unionid(可选)',
  `wx_nickname` VARCHAR(64) NOT NULL COMMENT '微信昵称',
  `game_nickname` VARCHAR(64) NOT NULL COMMENT '游戏昵称(默认=微信昵称，可修改)',
  `avatar_url` VARCHAR(512) DEFAULT NULL COMMENT '头像URL',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uk_openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息';


CREATE TABLE IF NOT EXISTS `scores` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `openid` VARCHAR(64) NOT NULL COMMENT '关联用户openid',
  `nickname` VARCHAR(64) NOT NULL COMMENT '用于展示的昵称(建议=users.game_nickname)',
  `score` INT NOT NULL DEFAULT 0 COMMENT '分数',
  `moves` INT NOT NULL DEFAULT 0 COMMENT '步数',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY `idx_score_desc` (`score` DESC),
  KEY `idx_moves` (`moves`),
  KEY `idx_openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='2048分数记录';