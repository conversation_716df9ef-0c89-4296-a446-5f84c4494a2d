# 🔍 排行榜数据调试指南

## 🚨 问题描述
分数提交成功，但排行榜显示的不是阿里云数据库中scores表的真实数据。

## 🔧 已完成的修复

### 1. 统一API配置 ✅
- 修复了硬编码的API地址
- 现在使用 `serverConfig.getApiUrl('getRanking')` 统一配置
- API地址：`https://api.huahang.me/api/game2048/ranking`

### 2. 增强数据格式处理 ✅
- 支持多种API响应格式：
  - 直接数组格式：`[{...}, {...}]`
  - 标准格式：`{success: true, data: [...]}`
  - 简单格式：`{data: [...]}`

### 3. 优化数据字段映射 ✅
- 支持数据库常见字段名：
  - `created_at` 或 `timestamp` 或 `createdAt`
  - `id` 或 `_id`
  - 处理缺失字段的默认值

### 4. 增加详细调试日志 ✅
- API请求地址日志
- 响应数据详情日志
- 数据处理过程日志

## 🧪 调试步骤

### 第一步：检查API响应
1. 打开微信开发者工具
2. 点击"🏆 华航榜"按钮
3. 查看控制台输出，寻找以下关键信息：

```
从阿里云服务器加载排行榜数据...
使用API地址: https://api.huahang.me/api/game2048/ranking?limit=50
API响应: {statusCode: 200, data: {...}}
API响应数据详情: {...}
```

### 第二步：分析数据格式
根据控制台输出，确认您的API返回格式：

**格式A - 直接数组：**
```json
[
  {
    "id": 1,
    "nickname": "用户1",
    "score": 2048,
    "moves": 100,
    "created_at": "2024-01-01T10:00:00Z"
  }
]
```

**格式B - 标准API格式：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "nickname": "用户1", 
      "score": 2048,
      "moves": 100,
      "created_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

**格式C - 简单包装格式：**
```json
{
  "data": [
    {
      "id": 1,
      "nickname": "用户1",
      "score": 2048, 
      "moves": 100,
      "created_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

### 第三步：验证数据库字段
确认您的阿里云数据库scores表的字段名：

**常见字段名对应：**
- 主键：`id` 或 `_id`
- 昵称：`nickname`
- 分数：`score`
- 步数：`moves`
- 创建时间：`created_at` 或 `timestamp` 或 `createdAt`

## 🔧 可能的问题和解决方案

### 问题1：API返回空数据
**症状：** 控制台显示"数据条数: 0"
**解决方案：**
1. 检查数据库中是否有数据
2. 确认API查询逻辑是否正确
3. 检查数据库连接是否正常

### 问题2：API返回格式不匹配
**症状：** 控制台显示"API返回格式错误"
**解决方案：**
1. 查看"API响应数据详情"日志
2. 根据实际格式调整代码
3. 联系后端开发者确认API格式

### 问题3：字段名不匹配
**症状：** 排行榜显示"匿名用户"或默认值
**解决方案：**
1. 检查数据库字段名
2. 修改 `processRankingData` 函数中的字段映射

### 问题4：网络请求失败
**症状：** 控制台显示网络错误
**解决方案：**
1. 检查服务器是否正常运行
2. 确认域名和端口配置
3. 检查SSL证书是否有效

## 🛠️ 自定义修复

如果您的API格式与预期不同，可以修改以下代码：

### 修改API响应处理（如果格式特殊）：
```javascript
// 在 loadRankingFromAPIWithRetry 函数中
if (res.statusCode === 200) {
    // 根据您的实际API格式修改这里
    if (res.data.你的字段名) {
        resolve(res.data.你的字段名);
    }
}
```

### 修改字段映射（如果字段名不同）：
```javascript
// 在 processRankingData 函数中
return {
    id: item.你的ID字段 || index,
    nickname: item.你的昵称字段 || '匿名用户',
    score: item.你的分数字段 || 0,
    moves: item.你的步数字段 || 0,
    timeAgo: this.formatTimeAgo(item.你的时间字段 || new Date()),
    rank: index + 1
};
```

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：

1. **控制台完整日志**（特别是API响应部分）
2. **数据库表结构**（字段名和类型）
3. **API接口文档**（如果有的话）
4. **一条示例数据**（从数据库中查询的真实数据）

这样我可以为您提供更精确的解决方案。

---

**🎯 目标：确保排行榜显示阿里云数据库scores表中的真实数据！**
