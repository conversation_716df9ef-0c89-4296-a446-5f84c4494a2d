<view class="container">
  <!-- 头部背景 -->
  <view class="header-bg">
    <view class="header-content">
      <!-- 未登录状态 -->
      <view wx:if="{{!isLoggedIn}}" class="login-section">
        <view class="avatar-placeholder">
          <text class="avatar-icon">👤</text>
        </view>
        <text class="login-text">点击登录</text>
        <button class="login-btn" bindtap="handleLogin">微信登录</button>
      </view>

      <!-- 已登录状态 -->
      <view wx:else class="user-section">
        <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        <view class="user-info">
          <text class="user-nickname">{{userInfo.nickName}}</text>
          <text class="user-desc">游戏昵称：{{userInfo.gameNickname || userInfo.nickName}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 游戏统计 -->
  <view class="stats-section">
    <text class="section-title">🎮 游戏统计</text>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{gameStats.totalGames}}</text>
        <text class="stat-label">总游戏次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{gameStats.bestScore}}</text>
        <text class="stat-label">最高分数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{gameStats.totalScore}}</text>
        <text class="stat-label">累计分数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{gameStats.ranking}}</text>
        <text class="stat-label">当前排名</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <text class="section-title">⚙️ 功能设置</text>
    
    <view class="menu-list">
      <view class="menu-item" bindtap="editProfile">
        <view class="menu-icon">✏️</view>
        <view class="menu-content">
          <text class="menu-title">编辑资料</text>
          <text class="menu-desc">修改昵称和个人信息</text>
        </view>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" bindtap="viewRanking">
        <view class="menu-icon">🏆</view>
        <view class="menu-content">
          <text class="menu-title">排行榜</text>
          <text class="menu-desc">查看华航游戏排行榜</text>
        </view>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" bindtap="gameHistory">
        <view class="menu-icon">📊</view>
        <view class="menu-content">
          <text class="menu-title">游戏记录</text>
          <text class="menu-desc">查看历史游戏记录</text>
        </view>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" bindtap="settings">
        <view class="menu-icon">⚙️</view>
        <view class="menu-content">
          <text class="menu-title">设置</text>
          <text class="menu-desc">音效、通知等设置</text>
        </view>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" bindtap="feedback">
        <view class="menu-icon">💬</view>
        <view class="menu-content">
          <text class="menu-title">意见反馈</text>
          <text class="menu-desc">提交建议和问题反馈</text>
        </view>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" bindtap="about">
        <view class="menu-icon">ℹ️</view>
        <view class="menu-content">
          <text class="menu-title">关于我们</text>
          <text class="menu-desc">华航小游戏 v1.0</text>
        </view>
        <text class="menu-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view wx:if="{{isLoggedIn}}" class="logout-section">
    <button class="logout-btn" bindtap="handleLogout">退出登录</button>
  </view>

  <!-- 版本信息 -->
  <view class="footer">
    <text class="footer-text">华航小游戏 v1.0</text>
    <text class="footer-text">让学习之余更有趣 🎉</text>
  </view>
</view>

<!-- 编辑昵称弹窗 -->
<view wx:if="{{showEditModal}}" class="modal-overlay" bindtap="closeEditModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">编辑昵称</text>
    </view>
    <view class="modal-body">
      <input class="nickname-input" 
             placeholder="请输入新昵称" 
             value="{{editNickname}}"
             bindinput="onNicknameInput"
             maxlength="10" />
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="closeEditModal">取消</button>
      <button class="modal-btn confirm" bindtap="saveNickname">保存</button>
    </view>
  </view>
</view>
